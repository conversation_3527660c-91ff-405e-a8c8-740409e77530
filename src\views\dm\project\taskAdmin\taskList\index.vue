<template>
  <div class="content">
    <searchList :from-data="selectFrom" @tabHeight="getTabHeight">
      <!-- isCurUser -->
      <el-col slot="btnList-col" :lg="24" style="margin-bottom: 20px">
        <el-button
          size="mini"
          icon="el-icon-search"
          type="primary"
          class="btn"
          @click="search(false)"
          >查询</el-button
        >
        <!-- 我的任务 -->
        <template v-if="isCurUser">
          <el-button
            size="mini"
            type="success"
            class="btn"
            @click="add"
            v-permission="['myLaunchTask_add']"
            >新增</el-button
          >
          <el-button
            size="mini"
            type="success"
            class="btn"
            @click="batchOperation"
            v-permission="['myLaunchTask_batch']"
            >批量代操作</el-button
          >
        </template>
        <template v-else>
          <el-button
            size="mini"
            type="success"
            class="btn"
            @click="add"
            v-permission="['taskList_add']"
            >新增</el-button
          >
          <el-button
            size="mini"
            type="success"
            class="btn"
            @click="batchOperation"
            v-permission="['taskList_batch']"
            >批量代操作</el-button
          >
          <el-button
            type="primary"
            v-permission="['taskList_export']"
            size="mini"
            class="btn"
            @click="exportxlsx"
            >导出</el-button
          >
        </template>
        <el-button
          size="mini"
          type="primary"
          class="btn"
          @click="batchSendSms"
          :loading="batchSendSmsLoading"
          v-permission="['taskList_Batch_sending_SMS']"
          >批量发送短信</el-button
        >
        <el-button v-permission="['taskList_Download_self_operated_templates']" size="mini" type="primary" @click="downloadExportTemp"
          >下载任务导入模版</el-button
        >
        <el-upload
          ref="upload"
          :headers="header"
          class="uploadBtn"
          :action="uploadexcel"
          :auto-upload="true"
          :on-success="uploadSuccess"
          :on-error="uploadError"
          :show-file-list="false"
          :before-upload="uploadProgress"
          accept=".xlsx,.xls"
          style="display: inline-block; margin: 0 10px"
          v-permission="['taskList_Batch_import_tasks']"
        >
          <el-button size="mini" type="primary" :loading="importLoading"
            >批量导入任务</el-button
          >
        </el-upload>
        <el-button
          size="mini"
          type="primary"
          class="btn"
          @click="fixSign"
          :loading="fixSignLoading"
          v-permission="['taskList_Check_in']"
          >修复地推签到打卡</el-button
        >

        <el-button
          :loading="exportProjectLoading"
          size="mini"
          class="btn"
          type="primary"
          @click="exportTaskProject()"
          v-permission="['taskList_Task_Progress']"
          >导出任务进度表</el-button
        >

        <el-upload
          ref="upload"
          :headers="header"
          class="uploadBtn"
          :action="importScheduleExcel"
          :auto-upload="true"
          :on-success="taskScheduleUploadSuccess"
          :on-error="taskScheduleUploadError"
          :show-file-list="false"
          :before-upload="taskScheduleUploadProgress"
          accept=".xlsx,.xls"
          style="display: inline-block; margin: 0 10px"
          v-permission="['taskList_Update_task']"
        >
          <el-button
            size="mini"
            type="primary"
            :loading="importTaskScheduleLoading"
            >导入更新任务进度</el-button
          >
        </el-upload>
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="downloadTaskScheduleTemp"
          v-permission="['taskList_Update_task']"
          >下载更新任务进度模板</el-button
        >
        <el-button
          type="primary"
          size="mini"
          :loading="generateOperationPlanLoading"
          @click="batchGenerateOperationPlan"
          v-permission="['taskList_Generate_Operations']"
        >批量生成运营计划</el-button>
        <el-button
          type="danger"
          size="mini"
          :loading="batchCancelLoading"
          @click="batchCancel"
          v-permission="['taskList_revoke']"
        >批量撤销</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchUpdatePrice"
          v-permission="['taskList_Modify_amount']"
        >批量修改金额</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchUpdateTaskPerformance"
          v-permission="['taskList_Cleaning_frequency']"
        >批量计算次数奖金</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchUpdateUnitPrice"
          v-permission="['taskList_Set_unit_price']"
        >批量设置单价</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchCreateLinkTask()"
          :loading='batchCreateLinkTaskLoading'
          v-permission="['taskList_Generate_channel_chain']"
        >批量生成渠道链任务</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchCloneCheckIn()"
          :loading='batchCloneCheckInLoading'
          v-permission="['taskList_Clone_check_in']"
        >批量修复签到打卡</el-button>
        <el-button
          type="primary"
          size="mini"
          :loading="generateWithdrawalLoading"
          v-permission="['taskList_Generate_withdrawal']"
          @click="generateWithdrawal()"
        >生成提现</el-button>
        <el-button
          type="primary"
          size="mini"
          :loading="batchModifyTaskStatusLoading"
          v-permission="['taskList_batch_modify_task_status']"
          @click="batchModifyTaskStatus()"
        >批量修改任务状态</el-button>
        <el-button
          type="danger"
          size="mini"
          :loading="batchDeleteTaskStatusLoading"
          v-permission="['taskList_batch_delete_task_status']"
          @click="batchDeleteTaskStatus()"
        >批量删除</el-button>
        <el-button
          type="warning"
          size="mini"
          :loading="batchAuditTaskStatusLoading"
          v-permission="['taskList_batch_audit_task_status']"
          @click="batchAuditTaskStatus()"
        >批量审核</el-button>
        <batchExportReport style="margin-left:10px" :selectArr='selectArr'></batchExportReport>
        <el-button
          style="margin-left: 10px;"
          type="primary"
          size="mini"
          @click="batchCloneToTenant"
        >克隆到租户</el-button>

        <el-button
          style="margin-left: 10px;"
          type="primary"
          size="mini"
          @click="batchUpdateThirdDataJson"
        >批量更新支付编码</el-button>

      </el-col>
      <template slot='ids' slot-scope="scope">
        <el-input
          type="textarea"
          placeholder="请输入任务编码,多个回车隔开"
          v-model="scope.row.value"         
        >
        </el-input>
      </template>
    </searchList>

    <el-alert type="warning" style="margin-bottom: 15px">
      <template slot="title">
        导入任务中如果有一条失败，其他导入成功的条数都会回滚
      </template>
    </el-alert>

    <!-- <div class="tabel" :style="{ height: tabHeight }"> -->
    <div class="tabel">
      <el-tabs
        v-model="lastDisposeTypeIndex"
        type="card"
        @tab-click="search(false)"
      >
        <el-tab-pane
          :label="item.label"
          :name="item.value"
          v-for="(item, index) in lastDisposeTypeList"
          :key="index"
        >
          <!-- <template slot="label">
          <span :style="item.style">
            {{ item.label }}
          </span>
        </template> -->
          <tab
            :table-data="tableData.records"
            :loading="loading"
            :isCurUser="isCurUser"
            @select="select"
            @showTab="showTab"
            @clickLoading="clickLoading"
            @export-report='previewAnalysisReportCloud'
          />
        </el-tab-pane>
      </el-tabs>
      <pagination
        :current="current"
        :size="size"
        :total="tableData.total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
        <!-- :pageSizes='[5, 10, 20, 30, 50]' -->
    </div>

    <add :show.sync="addShow" :paramsData="paramsData" @close="closeAdd" />
    <check
      :show.sync="checkShow"
      :paramsData="checkParamsData"
      :isCurUser="isCurUser"
      @close="closeCheck"
    />
    <finish
      :show.sync="finishShow"
      :paramsData="finishParamsData"
      @close="closeFinish"
    />
    <batch-operation
      :show.sync="batchOperationShow"
      :paramsData="batchOperationParamsData"
    />
    <exportXlsx
      filename="任务列表.xlsx"
      :data="selectArr"
      :width="'1280px'"
      :hearders="exportHeaders"
      :visible="exportvisible"
      @close="exportvisible = false"
      @query="exportvisible = false"
    ></exportXlsx>
    <import-record-tab
      :show.sync="importRecordShow"
      :tableData="importRecordTabdata"
    ></import-record-tab>

    <exportXlsx
      :filename="'任务进度表.xlsx'"
      :data="taskProgressData"
      :width="'1280px'"
      ref="exportTaskProgressDataRef"
      :hearders="exportTaskProgressDataHeaders"
      :visible="exportTaskProgressDatavisible"
      @close="exportTaskProgressDatavisible = false"
      @query="exportTaskProgressDatavisible = false"
    >
      <template slot='cellNext' slot-scope="scope">
        <!-- {{scope.uuid}} -->
        <template v-if='scope.uuid === "taskInitiation"'>
          <div>
            <template v-for='urlItem in scope.row.taskInitiationImg'>
              <el-image
                class="cell-img" :key="urlItem.url"
                :src="urlItem.url"
                :preview-src-list="[urlItem.url]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </template>
          </div>
        </template>
        <template v-else-if='scope.uuid === "taskProcessing"'>
          <div>
            <template v-for='urlItem in scope.row.taskProcessingImg'>
              <el-image
                class="cell-img" :key="urlItem.url"
                :src="urlItem.url"
                :preview-src-list="[urlItem.url]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </template>
          </div>
        </template>
        <template v-else-if='scope.uuid === "taskProcessed"'>
          <div>
            <template v-for='urlItem in scope.row.taskProcessedImg'>
              <el-image
                class="cell-img" :key="urlItem.url"
                :src="urlItem.url"
                :preview-src-list="[urlItem.url]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </template>
          </div>
        </template>
        <template v-else-if='scope.uuid === "taskCompleted"'>
          <div>
            <template v-for='urlItem in scope.row.taskCompletedImg'>
              <el-image
                class="cell-img" :key="urlItem.url"
                :src="urlItem.url"
                :preview-src-list="[urlItem.url]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </template>
          </div>
        </template>
      </template>
    </exportXlsx>

    <!-- 导出药店拜访数据 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef3"
        @compute="computeExport"
        :filename="visitFilename + '数据报告'"
        :exportType="2"
        :pageSize="visitDataUpdatecountExportCountPageSize"
        boxId="exportVisitingDataActivityReport"
        @updateWidth="updateDataWidth"
      >
        <exportVisitingDataActivityReport
          @compute="computeDataSaveTwo"
          :updatecount="visitDataUpdatecountExportCount"
          :taskId="visitTaskId"
          :filename="visitFilename"
          :pageSize="visitDataUpdatecountExportCountPageSize"
          @updateWidth="updateDataWidth"
        ></exportVisitingDataActivityReport>
      </exportPDF>
    </div>

    <el-dialog
      title="导出加载中..."
      :visible.sync="exportLoading"
      width="500px"
      :before-close="handleClose"
    >
      <!-- <span></span> -->
      <div class="exportLoadingBox" v-loading="exportLoading"></div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>

    <previewDataReport
      :visible="previewDataVisible"
      :updatecount="previewDataUpdateCount"
      :taskId="visitTaskId"
      @close="previewDataVisible = false"
      :filename="visitFilename"
    ></previewDataReport>

    <!-- 导出药店拜访分析数据 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef4"
        @compute="computeExport"
        :filename="visitFilename + '分析报告'"
        :exportType="2"
        :pageSize="visitAnalysisUpdatecountExportCountPageSize"
        boxId="exportVisitingAnalysisActivityReport"
        @updateWidth="updateDataWidth"
      >
        <exportVisitingAnalysisActivityReport
          @compute="computeAnalysisSaveTwo"
          :updatecount="visitAnalysisUpdatecountExportCount"
          :taskId="visitTaskId"
          :filename="visitFilename"
          :pageSize="visitAnalysisUpdatecountExportCountPageSize"
        ></exportVisitingAnalysisActivityReport>
        <!-- @updateWidth="updateDataWidth" -->
      </exportPDF>
    </div>

    <!-- 预览分析报告 previewAnalysisReport-->
    <previewAnalysisReport
      :visible="previewAnalysisVisible"
      :updatecount="previewAnalysisUpdateCount"
      :taskId="visitTaskId"
      @close="previewAnalysisVisible = false"
      :filename="visitFilename"
    ></previewAnalysisReport>
    <reportLog :exportName='exportName' :show="showReportLog" :pagetype="pagetype" :selectIds='selectIds' @close='closeReport' :landscape="landscape"></reportLog>
    <batch-update-price :show.sync="batchUpdatePriceShow" :params-data="selectArr" />
    <batch-update-unit-price :show.sync="batchUpdateUnitPriceShow" :params-data="selectArr" />
    <batchUpdateTaskPrice :visible="batchUpdateTaskPriceVisible" :taskInfos='selectArr' :updatecount="batchUpdateTaskPriceUpdateCount" @close='closeBatchUpdateTaskPrice'></batchUpdateTaskPrice>
    <!-- 批量生成运营计划 -->
    <generateOperationPlanDialog :visible='generateOperationPlanListDialogVisible' :listObject='generateOperationPlanList' :updatecount='generateOperationPlanListUpdateCount' @close='closeGenerateOperationPlanListDialog'></generateOperationPlanDialog>
    <!-- 克隆图片 -->
    <copyTaskImage :visible='copyTaskImageVisible' :updatecount="copyTaskImageUpdateCount" :paramsData="copyTaskImageParams" @close="closeCopyTaskImage"></copyTaskImage>
    <!-- 批量修改任务状态 -->
    <batchUpdateTaskStatus :visible="batchUpdateTaskStatusVisible" :selectArr='selectArr' :updatecount="batchUpdateTaskStatusUpdateCount" @close='closeBatchUpdateTaskStatus'></batchUpdateTaskStatus>
    <!-- 批量审核 -->
    <batchAudit :show='batchAuditShow' :updatecount="batchAuditUpdateCount" :selectArr="selectArr" @close='closeBatchAudit'></batchAudit>
    <!-- 批量修复签到打卡 -->
    <signFixV2 :show='signFixV2Show' :updatecount='signFixV2UpdateCount' :selectArr='selectArr' @close='closeSignFixV2'></signFixV2>
    <batch-clone-to-tenant :show.sync="batchCloneToTenantShow" :paramsData="batchCloneToTenantParamsData" @close="search(true)" />
    <batchUpdateThirdDataJson :show.sync="batchUpdateThirdDataJsonShow" :paramsData="batchUpdateThirdDataJsonParamsData" @close="search(true)" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getToken, getTokenName } from "@/utils/auth";
import { getFromData, parseTime, toNumber,domainURL } from "@/utils/index";
import {
  getTasksLastDisposeType,
  getTaskType,
  getPriority,
  getPayChannel,
  getDmCommonAuditStatus,
  commonList,
  getActivityInviteFinishStatusList
} from "@/utils/enumeration";


import tab from "./components/table.vue";
import pagination from "@/components/MyPagination";
import add from "./components/add";
import check from "./components/check";
import finish from "./components/finish";
import batchOperation from "./components/batch-operation/index";
import batchUpdatePrice from "./components/batch-update-price"
import batchUpdateUnitPrice from "./components/batch-update-unit-price"

import {
  queryPage,
  deleteBatch,
  update,
  closeTasks,
  sendTaskSms,
  batchSendTaskSms,
  importExcel as uploadexcel,
  signFix,
  importScheduleExcel,
  generateChannelTaskBatch,
  cancelBatch,
  todotasksChannelLinkGenerate,
  todotasksSignFixV2 as todotasksCloneSignFromOtherTenant,
} from "@/api/todotasks";
import { queryList as physicianinfoQueryList } from "@/api/physicianinfo";
import { queryList as dmDemandQueryList } from "@/api/dmDemand";

import exportXlsx from "@/components/exportXlsx/index.vue";
import importRecordTab from "./components/import-record-tab";
import {
  queryPage as todotasksitemQueryPage,
  // queryProgress,
} from "@/api/todotasksitem";

import { queryRecordList } from "@/api/todotasksitemoperating";

import { queryList as signinlogQueryList } from "@/api/dm/common/signinlog";
import exportPDF from "@/components/exportPDF/index";
import exportVisitingDataActivityReport from "@/components/exportPDF/template/export-visiting-data-activity-report.vue";
import previewDataReport from "./components/previewDataReport/index.vue";
import {
  getVisitingplan,
  // insert,
  // update,
} from "@/api/dm/visiting/visitingplan";
import { insertWithdrawalByTaskids } from '@/api/financewithdrawal.js'

import exportVisitingAnalysisActivityReport from "@/components/exportPDF/template/export-visiting-analysis-activity-report.vue";
import previewAnalysisReport from './components/previewAnalysisReport/index.vue'
import reportLog from '@/views/dm/components/report-log/index.vue'
import batchUpdateTaskPrice from './components/batch-update-task-price/index.vue'
import generateOperationPlanDialog from './components/generate-operation-plan-dialog/index.vue';
// 克隆图片
import copyTaskImage from './components/copy-task-image/index.vue';
// 批量生成任务状态
import batchUpdateTaskStatus from './components/batch-update-taskStatus/index.vue';
// 批量审核
import batchAudit from './components/batch-audit/index.vue';
// 批量修复签到打卡
import signFixV2 from './components/sign-fix-v2/index.vue';
import batchExportReport from './components/batch-export-report/index.vue';
import batchCloneToTenant from './components/batch-clone-to-tenant/index.vue'
import batchUpdateThirdDataJson from './components/batch-update-third-data-json/index.vue'
export default {
  props: {
    isCurUser: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    tab,
    pagination,
    add,
    check,
    finish,
    batchOperation,
    exportXlsx,
    importRecordTab,
    exportPDF,
    exportVisitingDataActivityReport,
    previewDataReport,
    exportVisitingAnalysisActivityReport,
    previewAnalysisReport,
    reportLog,
    batchUpdatePrice,
    batchUpdateTaskPrice,
    batchUpdateUnitPrice,
    generateOperationPlanDialog,
    copyTaskImage,
    batchUpdateTaskStatus,
    batchAudit,
    signFixV2,
    batchExportReport,
    batchCloneToTenant,
    batchUpdateThirdDataJson
  },
  data() {
    return {
      // 批量修复签到打卡
      signFixV2Show: false,
      signFixV2UpdateCount: 0,

      // 批量审核
      batchAuditTaskStatusLoading: false,
      batchAuditShow: false,
      batchAuditUpdateCount: 0,


      // 批量删除
      batchDeleteTaskStatusLoading:false,

      // 批量修改任务状态
      batchModifyTaskStatusLoading:false,
      batchUpdateTaskStatusVisible:false,
      batchUpdateTaskStatusUpdateCount:0,

      // 克隆图片
      copyTaskImageVisible:false,
      copyTaskImageUpdateCount:0,
      copyTaskImageParams:null,
      // 批量克隆签到
      batchCloneCheckInLoading:false,

      // 批量生成运营计划
      generateOperationPlanListDialogVisible:false,
      generateOperationPlanList:[],
      generateOperationPlanListUpdateCount:0,
      landscape:true,
      
      // 任务绩效
      batchUpdateTaskPriceUpdateCount:0,
      batchUpdateTaskPriceVisible:false,
      // 导出文件名
      exportName:"",
      // 导出分析报告云服务
      selectIds:[],
      showReportLog:false,
      pagetype:3,// 3 是问卷分析报告 5 问卷数据报告
      previewVisitAnalysisReportLoading:false,
      // 预览分析报告
      previewAnalysisVisible:false,
      previewAnalysisUpdateCount:0,

      exportVisitAnalysisReportLoading:false,
      // 导出分析报告
      visitAnalysisUpdatecountExportCountPageSize: {
        height: 595,
        width: 881,
      },
      visitAnalysisUpdatecountExportCount: 0,

      // 导出数据报告
      exportVisitDataReportLoading: false,
      previewDataLoading: false,

      previewDataVisible: false,
      previewDataUpdateCount: 0,

      exportLoading: false,

      visitTaskId: null,
      visitDataUpdatecountExportCount: 0,
      visitDataUpdatecountExportCountPageSize: {
        height: 595,
        width: 881,
      },
      visitFilename: "",

      // 导出任务进度表
      exportFileName: null,
      taskProgressData: [],
      exportTaskProgressDatavisible: false,
      exportTaskProgressDataHeaders: [
        { key: "mainId", title: "任务编码", width: 180 },
        { key: "demandTitles", title: "项目名称", width: 180 },
        { key: "taskTitlte", title: "任务标题", width: 180 },
        { key: "disposeUserName", title: "执行人", width: 180 },
        { key: "physicianPhone", title: "手机号码", width: 180 },
        { key: "physicianIdNumber", title: "身份证号", width: 180 },
        { key: "taskCategory", title: "任务类型", width: 180 },
        { key: "activityName", title: "活动名称", width: 180 },
        { key: "signText", title: "是否已签到", width: 220 },
        { key: "createTime", title: "创建时间", width: 180 },
        { key: "taskInitiation", title: "任务发起", width: 220 },
        { key: "taskProcessing", title: "任务处理中", width: 220 },
        { key: "taskProcessed", title: "任务已处理", width: 220 },
        { key: "taskCompleted", title: "任务已确定完成", width: 220 },
      ],
      exportProjectLoading: false,
      importScheduleExcel, // 导入更新任务进度
      importTaskScheduleLoading: false,

      selectArr: [],
      exportHeaders: [
        { key: "title", title: "标题", width: 180 },
        { key: "receiveUserNames", title: "执行人", width: 180 },
        { key: "incentiveFeePriceText", title: "金额", width: 180 },
        // { prop: 'projectName', label: '关联项目', width: '180px' },
        { key: "demandTitles", title: "所属项目", width: 180 },
        { key: "cutTime", title: "截止时间", width: 180 },
        { key: "taskTypeText", title: "任务类型", width: 100 },
        { key: "priorityText", title: "优先级", width: 100 },
        { key: "progress", title: "进度", width: 100 },
        { key: "lastDisposeTypeText", title: "处理状态", width: 100 },
        { key: "payChannelText", title: "支付渠道", width: 180 },
        { key: "auditStatusText", title: "审核状态", width: 180 },
        { key: "auditRemark", title: "审核备注", width: 180 },
        { key: "createTime", title: "创建时间", width: 180 },

        // { key: "physicianGroupName", title: "可见分组", width: 200 },
        // { key: "title", title: "标题", width: 180 },
        // { key: "typeText", title: "项目类型", width: 180 },
        // { key: "desc", title: "描述", width: 180 },
        // { key: "acceptanceCriteria", title: "验收标准", width: 180 },
        // { key: "demandStatusText", title: "项目状态", width: 100 },
        // { key: "startTime", title: "开始时间", width: 180 },
        // { key: "endTime", title: "结束时间", width: 180 },
      ],
      exportvisible: false,

      tabHeight: 0,
      tableData: {},
      current: 1,
      size: 10,
      loading: false,
      selectFrom: [
        { title: "标题", id: "title", value: null, type: 1, option: [] },
        {
          title: "关闭状态",
          id: "initiatorCloseStatus",
          value: null,
          type: 2,
          option: [
            { value: 1, label: "未关闭" },
            { value: 2, label: "已关闭" },
          ],
        },
        
        { title: "姓名", id: "userId", value: null, type: 2, option: [],multiple:true },
        { title: "所属项目", id: "demandId", value: null, type: 2, option: [] },
        {
          title: "任务类型",
          id: "taskType",
          value: null,
          // value: 6,
          type: 2,
          option: getTaskType(),
        },
        {
          title: "支付渠道",
          id: "payChannel",
          value: null,
          type: 2,
          option: getPayChannel(),
        },

        { title: "关联的活动", id: "activityName", value: null, type: 1, option: [] },
        {
          title: "审核状态",
          id: "auditStatus",
          value: null,
          type: 2,
          option: getDmCommonAuditStatus(),
        },
        {
          title: "截止时间范围",
          id: "cutTime",
          value: [],
          option: [],
          type: 6,
          defaultTime: ["00:00:00", "23:59:59"],
          dateType: "datetimerange",
          clearable: true,
        },
        {
          title: "创建时间范围",
          id: "createTime",
          value: [],
          option: [],
          type: 6,
          defaultTime: ["00:00:00", "23:59:59"],
          dateType: "datetimerange",
          clearable: true,
        },
        { title: "任务编码", id: "ids", value: null, type: 20, option: [] },
        { title: "是否存在任务照片", id: "hasImages", value: null, type: 2, option: commonList() },
        { title: "任务进度处理状态", id: "processStatus", value: null, type: 2, option: getTasksLastDisposeType().filter(item => [3,4].includes(item.value)) },
        { title: "KPI 完成状态", id: "activityInviteFinishStatus", value: null, type: 2, option: getActivityInviteFinishStatusList() },
        { title: "操作时间", id: "processTime", value: null, type: 6, option: [], defaultTime: ["00:00:00", "23:59:59"], dateType: "datetimerange", clearable: true },
        { title: "提现确认审核时间", id: "financeAuditTime", value: null, type: 6, option: [], defaultTime: ["00:00:00", "23:59:59"], dateType: "datetimerange", clearable: true },

      ],
      paramsData: null,
      addShow: false,
      checkParamsData: null,
      checkShow: false,
      finishParamsData: null,
      finishShow: false,
      lastDisposeTypeList: [
        { value: "0", label: "全部" },
        ...getTasksLastDisposeType()
          .filter((item) => ![1, 7, 8, 9].includes(item.value))
          .map((item) => {
            return { ...item, value: item.value + "" };
          }),
      ],
      lastDisposeTypeIndex: "0",
      batchOperationShow: false,
      batchOperationParamsData: null,
      batchSendSmsLoading: false,
      header: {
        [getTokenName()]: getToken(),
        "auth-version": "v2",
      },
      uploadexcel,
      importRecordShow: false,
      importRecordTabdata: [],
      importLoading: false,
      fixSignLoading: false,
      generateOperationPlanLoading: false,
      batchCancelLoading: false,
      batchUpdatePriceShow: false,
      batchUpdateUnitPriceShow: false,
      batchCreateLinkTaskLoading:false,
      generateWithdrawalLoading: false,
      batchCloneToTenantShow: false,
      batchCloneToTenantParamsData: null,
      batchUpdateThirdDataJsonShow: false,
      batchUpdateThirdDataJsonParamsData: null
    };
  },
  created() {
    if (this.permissions['task_type_business_dev']) {
      this.setFormData('taskType', 'value', 19)
      this.setFormData('taskType', 'hidden', true)
    }
    this.physicianinfoQueryList();
    this.dmDemandQueryList();
    this.search();
  },
  computed: {
    ...mapGetters(['permissions']),
    loginid() {
      let loginid = localStorage.getItem("loginId");
      loginid = loginid ? JSON.parse(loginid) : null;
      return loginid;
    },
  },
  watch: {
    importRecordShow() {
      if (!this.importRecordShow) {
        this.importRecordTabdata = [];
      }
    },
    // addShow() {
    //   if (!this.addShow) {
    //     this.paramsData = null;
    //     this.search(true);
    //   }
    // },
    // checkShow() {
    //   if (!this.checkShow) {
    //     this.checkParamsData = null;
    //     this.search(true);
    //   }
    // },
    // finishShow() {
    //   if (!this.finishShow) {
    //     this.finishParamsData = null;
    //     this.search(true);
    //   }
    // },
    batchOperationShow() {
      if (!this.batchOperationShow) {
        this.search(true);
      }
    },
    batchUpdatePriceShow() {
      if (!this.batchUpdatePriceShow) {
        this.search(true);
      }
    },
    batchUpdateUnitPriceShow() {
      if (!this.batchUpdateUnitPriceShow) {
        this.search(true);
      }
    }
  },
  methods: {
    batchUpdateThirdDataJson() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('至少选中一个任务');
      }

      this.batchUpdateThirdDataJsonShow = true;
      this.batchUpdateThirdDataJsonParamsData = this.selectArr;
    },
    batchCloneToTenant() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('至少选中一个任务');
      }

      for (let i = 0; i < this.selectArr.length; i++) {
        if (![29,31].includes(this.selectArr[i].taskType)) {
          return this.$eltool.errorMsg('只有科普帖子、科普笔记类型的任务才能进行克隆到租户');
        }
      }

      this.batchCloneToTenantShow = true;
      this.batchCloneToTenantParamsData = this.selectArr;
    },
    // 批量修复签到打卡
    closeSignFixV2(type) {
      if(type === 'query') {
        this.search(true)
      }
      this.signFixV2Show = false;
    },
    closeBatchAudit(type) {
      if(type === 'query') {
        this.search(true)
      }
      this.batchAuditShow = false;
    },
    // 批量审核
    async batchAuditTaskStatus() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('至少选中一个任务');
      }
      this.batchAuditShow = true;
      this.$nextTick(() => {
        this.batchAuditUpdateCount += 1;
      })
    },
    async batchDeleteTaskStatus() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('至少选中一个任务');
      }
      let ids = this.selectArr.filter(item => item.lastDisposeType === 4);
      if(ids.length !== 0) {
        return this.$eltool.errorMsg('选中批量删除任务中不能包括已处理的任务');
      }


      this.$confirm("此操作将批量删除任务, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.batchDeleteTaskStatusLoading = true;
        const res = await deleteBatch({
          ids: this.selectArr.map(item => item.id).join(',')
        }).catch(e => {
          this.batchDeleteTaskStatusLoading = false;
        })
        this.batchDeleteTaskStatusLoading = false;
        this.$eltool.successMsg(res.msg);
        this.search(true)
      })
    },
    closeBatchUpdateTaskStatus(type) {
      if(type === 'query') {
        this.search(true)
      }
      this.batchUpdateTaskStatusVisible = false;
      this.batchModifyTaskStatusLoading = false;

    },
    // 批量修改任务状态
    batchModifyTaskStatus() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      this.batchModifyTaskStatusLoading = true;
      this.batchUpdateTaskStatusVisible = true;
      this.$nextTick(() => {
        this.batchUpdateTaskStatusUpdateCount += 1;
      })

    },
    closeCopyTaskImage(type){
      if(type === 'query') {
        this.search(true)
      }
      this.copyTaskImageVisible = false;
    },
    // 生成提现
    async generateWithdrawal() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      this.$confirm("是否执行生成提现?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {

        this.generateWithdrawalLoading = true;
        const res = await insertWithdrawalByTaskids({
          taskIds: this.selectArr.map(item => item.id).join(',')
        }).catch(() => {
          this.generateWithdrawalLoading = false
        })
        this.generateWithdrawalLoading = false
        if (res.data) {
          return this.$eltool.errorMsg(res.data)
        }
        this.$eltool.successMsg('操作成功')
      })
    },
    // 批量克隆签到
    async batchCloneCheckIn() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('必须选中一条任务')
      }
      let ids = this.selectArr.filter(item => item.lastDisposeType === 4);
      if(ids.length === 0) {
        return this.$eltool.errorMsg('选中任务中没有一条已处理的条目')
      }
      this.signFixV2Show = true;
      this.$nextTick(() => {
        this.signFixV2UpdateCount += 1;
      })
    },
    // 批量生成渠道链
    async batchCreateLinkTask() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('请选择要操作的条目')
      }
      let is = true;
      let ids = [];
      for(let i=0;i<this.selectArr.length;i++) {
        if([7,12,30].includes(this.selectArr[i].taskType)) {
          ids.push(this.selectArr[i].id)
        }else {
          is = false;
        }
      }
      if(ids.length === 0) {
        return this.$eltool.errorMsg('必须选中一条任务类型为【自营地推类（新）或 自营地推类（旧）或用户活动】的数据，才能操作此')
      }
      if(!is) {
        this.$confirm("检测到你选中的不全是任务类型为【自营地推类（新）或 自营地推类（旧）或用户活动】的数据，默认只会对任务类型为【自营地推类（新）或 自营地推类（旧）】的数据进行操作，是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.batchCreateLinkTaskLoading = true;
          const res = await todotasksChannelLinkGenerate({
            ids:ids.join(',')
          }).catch(e => {
            this.batchCreateLinkTaskLoading = false
          })
          this.batchCreateLinkTaskLoading = false
          this.$eltool.successMsg(res.msg)
          this.search(true)
        })
      }else {
        this.$confirm("是否执行批量生成渠道链任务?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.batchCreateLinkTaskLoading = false
          const res = await todotasksChannelLinkGenerate({
            ids:ids.join(',')
          }).catch(e => {
            this.batchCreateLinkTaskLoading = false
          })
          this.batchCreateLinkTaskLoading = false
          this.$eltool.successMsg(res.msg)
          this.search(true)
        })
      }
      
    },
    closeGenerateOperationPlanListDialog() {
      this.search(true)
      this.generateOperationPlanListDialogVisible = false
    },
    closeBatchUpdateTaskPrice(type){
      if(type === 'query') {
        this.search(true)
      }
      this.batchUpdateTaskPriceVisible = false;
    },
    // 批量修正任务绩效
    async batchUpdateTaskPerformance(){
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('请选择要操作的条目')
      }
      this.batchUpdateTaskPriceVisible = true;
      this.$nextTick(() => {
        this.batchUpdateTaskPriceUpdateCount += 1;
      })
    },
    batchUpdateUnitPrice() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      this.batchUpdateUnitPriceShow = true
    },
    batchUpdatePrice() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      const is = this.selectArr.every(item => [4, 5].includes(item.lastDisposeType))
      if (!is) {
        return this.$eltool.errorMsg("只有已处理和已关闭的任务才能进行修改金额");
      }
      this.batchUpdatePriceShow = true
    },
    // 导出问卷/征集报告（云服务）
    previewAnalysisReportCloud({
      row,
      pagetype,
      fileName
    }){
      this.selectIds = [
        {
          taskId:row.id,// 任务id
        }
      ]
      this.pagetype = pagetype;
      this.exportName = fileName
      this.landscape = true;

      this.$nextTick(() => {
        this.showReportLog = true;
      })
    },
    closeReport(){
      this.showReportLog = false;
    },
    batchCancel() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      const is = this.selectArr.every(item => [2, 3].includes(item.lastDisposeType))
      if (!is) {
        return this.$eltool.errorMsg("只有待处理和处理中的任务才能进行撤销");
      }
      this.$confirm("是否确认撤销?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const params = this.selectArr.map(item => item.id)
        this.batchCancelLoading = true
        const res = await cancelBatch(params).catch(() => {
          this.batchCancelLoading = false
        })
        this.$eltool.successMsg(res.msg)
        this.batchCancelLoading = false
        this.search(true)
      })
    },
    // 批量生成运营计划
    async batchGenerateOperationPlan() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }
      const is = this.selectArr.every(item =>  [3,27,30,32].includes(item.taskType))
      if (!is) {
        return this.$eltool.errorMsg("必须选择的是问卷类任务/线上推广类/线上用户活动类/线下用户活动类任务");
      }
      if (this.selectArr.length > 10) {
        return this.$eltool.errorMsg('单次操作只能是10条')
      }
      const params = {
        idList: this.selectArr.map(item => item.id)
      }
      this.generateOperationPlanLoading = true
      const res = await generateChannelTaskBatch(params).catch(() => {
        this.generateOperationPlanLoading = false
      })
      // this.$eltool.successMsg(res.msg)
      this.generateOperationPlanLoading = false
      if(res && res.data instanceof Object) {
        let failCount = 0;
        for(let key in res.data) {
          failCount += 1;
        }
        if(failCount === 0) {
          this.$eltool.successMsg(res.msg)
          this.search(true)
          return; 
        }
        this.generateOperationPlanList = res.data;
        this.generateOperationPlanListDialogVisible = true;
        this.$nextTick(() => {
          this.generateOperationPlanListUpdateCount += 1;
        })

      }else if(res && res.code === 0) {
        this.search(true)
        this.$eltool.successMsg(res.msg)
      }
    },
    computeAnalysisSaveTwo() {
      this.$refs.exportPdfRef4.loadPdf()
    },
    handleClose() {
      this.exportLoading = false;
    },
    computeExport() {
      this.exportLoading = false;
      this.$eltool.successMsg("导出成功，详情请看下载记录");
    },
    updateDataWidth(width) {
      this.visitDataUpdatecountExportCountPageSize.width = width;
    },
    computeDataSaveTwo() {
      // 导出报告
      this.$refs.exportPdfRef3.loadPdf();
    },
    async exportVisitDataReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      if (item.taskType !== 6) {
        return this.$eltool.errorMsg("必须选择的是拜访类任务");
      }

      this.exportVisitDataReportLoading = true;

      const res = await getVisitingplan({
        id: item.businessId,
      });

      const data = res.data;

      this.exportVisitDataReportLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }
      this.exportLoading = true;

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.$nextTick(() => {
        this.visitDataUpdatecountExportCount += 1;
      });
    },
    async previewVisitDataReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      if (item.taskType !== 6) {
        return this.$eltool.errorMsg("必须选择的是拜访类任务");
      }
      // this.exportLoading = true;
      this.previewDataLoading = true;

      const res = await getVisitingplan({
        id: item.businessId,
      });

      const data = res.data;
      this.previewDataLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.previewDataVisible = true;
      this.$nextTick(() => {
        this.previewDataUpdateCount += 1;
      });
    },
    async exportAnalysisReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      if (item.taskType !== 6) {
        return this.$eltool.errorMsg("必须选择的是拜访类任务");
      }

      this.exportVisitAnalysisReportLoading = true;

      const res = await getVisitingplan({
        id: item.businessId,
      });

      const data = res.data;

      this.exportVisitAnalysisReportLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      this.exportLoading = true;

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.$nextTick(() => {
        this.visitAnalysisUpdatecountExportCount += 1;
      });
      // this.visitAnalysisUpdatecountExportCount += 1
    },
    async previewAnalysisReport() {

       if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      if (item.taskType !== 6) {
        return this.$eltool.errorMsg("必须选择的是拜访类任务");
      }

      this.previewVisitAnalysisReportLoading = true;

      const res = await getVisitingplan({
        id: item.businessId,
      });

      const data = res.data;

      this.previewVisitAnalysisReportLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      this.previewAnalysisVisible = true;
      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.$nextTick(() => {
        this.previewAnalysisUpdateCount += 1
      })

      //  previewAnalysisVisible:false,
      // previewAnalysisUpdateCount:0,
    },

    // 下载导入模板
    downloadTaskScheduleTemp() {
      window.open(
        "https://file.greenboniot.cn/static/execl/dm/task_schedule.xlsx"
      );
    },
    taskScheduleUploadProgress() {
      this.importTaskScheduleLoading = true;
    },
    taskScheduleUploadError(res) {
      this.importTaskScheduleLoading = false;
      this.$eltool.errorMsg(res.msg);
    },
    taskScheduleUploadSuccess(res) {
      this.importTaskScheduleLoading = false;
      this.importRecordShow = true;
      this.importRecordTabdata = !this.$validate.isNull(res.data)
        ? res.data
        : [];
      this.search();
      this.$message({
        message: res.msg,
        type: "success",
      });
    },
    sheepExport() {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          this.$refs.exportTaskProgressDataRef.query();
          setTimeout(() => {
            resolve(true);
          }, 1500);
        });
      });
    },
    // 导出任务进度表
    async exportTaskProject() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      }

      this.exportProjectLoading = true;

      const exportProjectData = [];

      for (let k = 0; k < this.selectArr.length; k++) {
        const row = this.selectArr[k];
        this.exportFileName = row.title;

        const condition = {
          mainId: row.id,
        };

        const res = await todotasksitemQueryPage({
          size: 100,
          current: 1,
          condition,
        }).catch((e) => {
          this.exportProjectLoading = false;
        });

        const data = res.data.records;

        for (let i = 0; i < data.length; i++) {
          data[i].name = row.title;
          data[i].taskCategory = row.taskTypeText;
          data[i].taskTitlte = row.title;
          data[i].activityName = row.activityName;
          // data[i].physicianIdNumber = item.physicianIdNumber;
          const tempRes = await queryRecordList({ itemId: data[i].id }).catch(
            (e) => {
              this.exportProjectLoading = false;
            }
          );

          // <!-- disposeType 任务状态 1-发起 2-待处理 3-处理中 4-已处理 5-已关闭 6-已撤销 7-已转办 8-已拒绝 9-已确认完成 -->

          const tempData = tempRes.data;

          for (let j = 0; j < tempData.length; j++) {
            let item = tempData[j];

            if (item.processStatus === 1) {
              // 发起
              if (!data[i].taskInitiation) {
                data[
                  i
                ].taskInitiation = `${item.createUser.recordName}（${item.processTime}）`;
                data[i].taskInitiationImg = [];
                let taskInitiationImg = item.imageAttachmentList;
                if(Array.isArray(taskInitiationImg)){
                  for(let ii=0;ii<taskInitiationImg.length;ii++){
                    data[i].taskInitiationImg.push({
                      url:domainURL(taskInitiationImg[ii].dir)
                    })
                  }
                }
              } else {
                data[i].taskInitiation +=
                  "," + `${item.createUser.recordName}（${item.processTime}）`;
                let taskInitiationImg = item.imageAttachmentList;
                // data[i].taskInitiationImg = [];
                if(Array.isArray(taskInitiationImg)){
                  for(let ii=0;ii<taskInitiationImg.length;ii++){
                    data[i].taskInitiationImg.push({
                      url:domainURL(taskInitiationImg[ii].dir)
                    })
                  }
                }
              }
            } else if (item.processStatus === 3) {
              // 处理中
              if (!data[i].taskProcessing) {
                data[
                  i
                ].taskProcessing = `${item.createUser.recordName}（${item.processTime}）`;
                let taskProcessingImg = item.imageAttachmentList;
                data[i].taskProcessingImg = [];
                if(Array.isArray(taskProcessingImg)){
                  for(let ii=0;ii<taskProcessingImg.length;ii++){
                    data[i].taskProcessingImg.push({
                      url:domainURL(taskProcessingImg[ii].dir)
                    })
                  }
                }
              } else {
                data[i].taskProcessing +=
                  "," + `${item.createUser.recordName}（${item.processTime}）`;
                let taskProcessingImg = item.imageAttachmentList;
                // data[i].taskProcessingImg = [];
                if(Array.isArray(taskProcessingImg)){
                  for(let ii=0;ii<taskProcessingImg.length;ii++){
                    data[i].taskProcessingImg.push({
                      url:domainURL(taskProcessingImg[ii].dir)
                    })
                  }
                }
              }
            } else if (item.processStatus === 4) {
              // 已处理
              if (!data[i].taskProcessed) {
                data[
                  i
                ].taskProcessed = `${item.createUser.recordName}（${item.processTime}）`;
                data[i].taskProcessedImg = [];
                let taskProcessedImg = item.imageAttachmentList;
                if(Array.isArray(taskProcessedImg)){
                  for(let ii=0;ii<taskProcessedImg.length;ii++){
                    data[i].taskProcessedImg.push({
                      url:domainURL(taskProcessedImg[ii].dir)
                    })
                  }
                }
              } else {
                data[i].taskProcessed +=
                  "," + `${item.createUser.recordName}（${item.processTime}）`;
                // data[i].taskProcessedImg = [];
                let taskProcessedImg = item.imageAttachmentList;
                if(Array.isArray(taskProcessedImg)){
                  for(let ii=0;ii<taskProcessedImg.length;ii++){
                    data[i].taskProcessedImg.push({
                      url:domainURL(taskProcessedImg[ii].dir)
                    })
                  }
                }
              }
            } else if (item.processStatus === 9) {
              // 已确认完成
              if (!data[i].taskCompleted) {
                data[
                  i
                ].taskCompleted = `${item.createUser.recordName}（${item.processTime}）`;
                let taskCompletedImg = item.imageAttachmentList;
                data[i].taskCompletedImg = [];
                if(Array.isArray(taskCompletedImg)){
                  for(let ii=0;ii<taskCompletedImg.length;ii++){
                    data[i].taskCompletedImg.push({
                      url:domainURL(taskCompletedImg[ii].dir)
                    })
                  }
                }
              } else {
                data[i].taskCompleted +=
                  "," + `${item.createUser.recordName}（${item.processTime}）`;
                let taskCompletedImg = item.imageAttachmentList;
                // data[i].taskCompleted = [];
                if(Array.isArray(taskCompletedImg)){
                  for(let ii=0;ii<taskCompletedImg.length;ii++){
                    data[i].taskCompletedImg.push({
                      url:domainURL(taskCompleted[ii].dir)
                    })
                  }
                }
              }
            }
          }
          // 发起
          if(data[i].taskInitiationImg && data[i].taskInitiationImg.length !== 0){
            data[i].taskInitiation += '[有图片]'
          }
          if(data[i].taskProcessingImg && data[i].taskProcessingImg.length !== 0){
            data[i].taskProcessing += '[有图片]'
          }
          if(data[i].taskProcessedImg && data[i].taskProcessedImg.length !== 0){
            data[i].taskProcessed += '[有图片]'
          }
          if(data[i].taskCompletedImg && data[i].taskCompletedImg.length !== 0){
            data[i].taskCompleted += '[有图片]'
          }

          // 获取签到状态
          const signRes = await signinlogQueryList({
            businessType: 2,
            businessId: data[i].id,
          });

          const signData = signRes.data;

          data[i].signText = signData.length > 0 ? "是" : "否";
          data[i].demandTitles = row.demandTitles;

          exportProjectData.push(data[i]);
        }
        console.log('exportProjectData',exportProjectData)

        // this.taskProgressData = data;

        // exportProjectData.concat(data);

        // this.exportTaskProgressDatavisible = true;

        // await this.sheepExport();
      }

      this.exportProjectLoading = false;

      this.taskProgressData = exportProjectData;

      this.exportTaskProgressDatavisible = true;

      // this.$nextTick(() => {
      //   // this.$refs.exportTaskProgressDataRef.query();
      //   // setTimeout(() => {
      //   //   resolve(true);
      //   // }, 1500);
      // });
    },
    // 修复地推签到打卡
    async fixSign() {
      if (this.$validate.isNull(this.selectArr)) {
        this.$eltool.warnMsg("请选中至少一个任务");
        return;
      }
      const params = this.selectArr.map((item) => item.id);
      this.fixSignLoading = true;
      const res = await signFix(params).catch(() => {
        this.fixSignLoading = false;
      });
      this.fixSignLoading = false;
      this.$eltool.successMsg(res.msg);
      this.search(true);
    },
    closeAdd({type}) {
      this.paramsData = null;

      if (type === "query") {
        this.search();
      }

      this.addShow = false;
    },
    closeFinish({ type }) {
      this.finishParamsData = null;

      if (type === "query") {
        this.search(true);
      }

      this.finishShow = false;
    },
    closeCheck({ type }) {
      // console.log('type',type)
      this.checkParamsData = null;

      // if (type === "query") {
      this.search(true);
      // }

      this.checkShow = false;
    },
    uploadProgress() {
      this.importLoading = true;
    },
    uploadError(res) {
      this.importLoading = false;
      this.$eltool.errorMsg(res.msg);
    },
    uploadSuccess(res) {
      this.importLoading = false;
      this.importRecordShow = true;
      this.importRecordTabdata = !this.$validate.isNull(res.data)
        ? res.data
        : [];
      this.search();
      this.$message({
        message: res.msg,
        type: "success",
      });
    },
    // 下载导入模板
    downloadExportTemp() {
      window.open(
        "https://file.greenboniot.cn/static/execl/dm/task_channel_export_template.xlsx"
      );
    },
    async batchSendSms() {
      if (this.$validate.isNull(this.selectArr)) {
        this.$eltool.warnMsg("请选中至少一个任务");
        return;
      }
      const is = this.selectArr.some((item) => item.lastDisposeType === 2);
      if (!is) {
        this.$eltool.warnMsg("任务进度必须为待处理");
        return;
      }
      const ids = this.selectArr.map((item) => item.id).join(",");
      this.batchSendSmsLoading = true;
      const res = await batchSendTaskSms({ ids }).catch(
        () => (this.batchSendSmsLoading = false)
      );
      this.batchSendSmsLoading = false;
      this.$eltool.successMsg(res.msg);
      this.search(true);
    },
    exportxlsx() {
      if (this.selectArr.length == 0) {
        this.$eltool.errorMsg("请选择要导出条目");
        return
      } else {
      }

      this.exportvisible = true;
    },

    async dmDemandQueryList() {
      const res = await dmDemandQueryList();
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.title,
        };
      });
      this.setFormData("demandId", "option", data);
    },
    clickLoading({ type, index }) {
      this.tableData.records[index].exLoading = type == "open";

      // this.$forceUpdate()
    },
    batchOperation() {
      if (this.$validate.isNull(this.selectArr)) {
        this.$eltool.warnMsg("请选中至少一个任务进行批量操作！");
        return;
      }
      this.batchOperationShow = true;
      this.batchOperationParamsData = this.selectArr;
    },
    async physicianinfoQueryList() {
      // attributionType 1-众包 2-企业内部
      const res = await physicianinfoQueryList({ attributionType: 2 });
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.userId,
          label: `${item.name} - ${item.phone}`,
        };
      });
      this.setFormData("userId", "option", data);
    },
    add() {
      this.paramsData = null;
      this.addShow = true;
    },
    async showTab({ type, row }) {
      console.log(row);
      const curIndex = this.tableData.records.findIndex(
        (item) => item.id === row.id
      );
      switch (type) {
        case 1:
          // 查看
          this.checkParamsData = row;
          this.checkShow = true;
          break;
        case 2:
          // 编辑
          this.paramsData = row;
          this.addShow = true;
          break;
        case 3:
          // 结案
          this.finishParamsData = row;
          this.finishShow = true;
          break;
        case 4:
          // 撤销
          this.$confirm("是否确认撤销?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            update({ ...row, inactiveStatus: 2, lastDisposeType: 6 }).then(
              (res) => {
                this.$eltool.successMsg(res.msg);
                this.search();
              }
            );
          });
          break;
        case 5:
          // 关闭
          this.$confirm("是否确认关闭?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            closeTasks({ id: row.id }).then((res) => {
              this.$eltool.successMsg(res.msg);
              this.search();
            });
          });
          break;
        case 6:
          // 短信提醒
          this.$confirm("是否确认发送短信提醒?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.$set(
              this.tableData,
              "records",
              this.tableData.records.map((item, index) => {
                return {
                  ...item,
                  sendTaskSmsLoading: index === curIndex ? true : false,
                };
              })
            );
            sendTaskSms({ id: row.id })
              .then((res) => {
                this.$eltool.successMsg(res.msg);
                this.search();
              })
              .catch(() => {
                this.$set(
                  this.tableData,
                  "records",
                  this.tableData.records.map((item) => {
                    return { ...item, sendTaskSmsLoading: false };
                  })
                );
              });
            this.$set(
              this.tableData,
              "records",
              this.tableData.records.map((item) => {
                return { ...item, sendTaskSmsLoading: false };
              })
            );
          });
          break;
        case 7:
          let receiveUserIds = []
          if(row.receiveUserIds && row.receiveUserIds !== ''){
            receiveUserIds = JSON.parse(row.receiveUserIds);
          }
          let fileName = receiveUserIds[0] ? receiveUserIds[0].recordName : '';
          if(fileName !== '') {
            fileName += '_'
          }
          if(row.taskType === 27) {
            fileName += '小葫芦线上推广活动执行明细'
            this.pagetype = 14;
          } else if(row.taskType === 30) {
            fileName += '线上用户活动'
            this.pagetype = 16;
          } else if(row.taskType === 32) {
            fileName += '线下用户活动'
            this.pagetype = 17;
          } else if(row.taskType === 31) {
            fileName += '科普笔记';
            this.pagetype = 20
          } else {
            this.pagetype = 12;
            fileName += '小葫芦精准地推活动执行明细';
          }
          let uparams = {

          }
          if([12,14,16,17,20].includes(this.pagetype)) {
            uparams.businessId = row.id
            uparams.businessLevelId = receiveUserIds[0].userId;
          }
          // 个人报告
          this.selectIds = [
            {
              taskId:row.id,// 任务id
              ...uparams
            }
          ]

          this.exportName = fileName;
          this.landscape = false;
          this.$nextTick(() => {
            this.showReportLog = true;
          })
          break;
        case 8:
          // copy-task-image
          this.copyTaskImageVisible = true;
          this.copyTaskImageParams = row;
          this.$nextTick(() => {
            this.copyTaskImageUpdateCount += 1
          })

          break;
        default:
      }
    },
    select(val) {
      this.selectArr = val;
    },
    setFormData(id, key, value) {
      this.selectFrom.forEach((item, index) => {
        if (item.id === id) {
          this.selectFrom[index][key] = value;
        }
      });
    },
    handleSizeChange(val) {
      this.size = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.current = val;
      this.search(true);
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    getEnumColor(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.style : "";
    },
    getTabHeight(height) {
      this.tabHeight = height;
    },
    search(noreset) {
      if (!noreset) {
        this.size = 10;
        this.current = 1;
      }
      this.loading = true;
      const size = this.size;
      const current = this.current;

      let condition = getFromData(this.selectFrom);
      const [cutTimeStart = null, cutTimeEnd = null] = condition.cutTime || [];
      const [createTimeStart = null, createTimeEnd = null] =
        condition.createTime || [];
      const [startProcessTime = null, endProcessTime = null] = condition.processTime || [];
      const [financeAuditStartTime = null, financeAuditEndTime = null] = condition.financeAuditTime || [];
      delete condition.processTime
      delete condition.startTime
      delete condition.endTime
      let userId = null;
      if(Array.isArray(condition.userId)) {
        userId = condition.userId.join(',')
      }
      condition = {
        ...condition,
        createUser: this.isCurUser ? this.loginid : "",
        lastDisposeType:
          this.lastDisposeTypeIndex === "0" ? "" : this.lastDisposeTypeIndex,
        receiveUserIds:userId,
        cutTimeStart,
        cutTimeEnd,
        createTimeStart,
        createTimeEnd,
        startProcessTime,
        endProcessTime,
        financeAuditStartTime,
        financeAuditEndTime
      };
      delete condition.cutTime;
      delete condition.createTime;
      if(condition.ids && condition.ids.length !== 0 && condition.ids.trim().length !== 0){
        let nameStrList = condition.ids.split('\n').filter(a => a !== '').map(str => str.trim());
        condition.ids = nameStrList.length === 0 ? null : nameStrList
      } else {
        delete condition.ids;
      }
      queryPage({ size, current, condition })
        .then((res) => {
          const getTasksLastDisposeTypeArr = getTasksLastDisposeType();
          const getPriorityArr = getPriority();
          res.data.records = res.data.records.map((item) => {
            let receiveUserIds = item.receiveUserIds ? JSON.parse(item.receiveUserIds) : []
            let operatingImageIdsText = item.operatingImageIds !== '' && item.operatingImageIds ? item.operatingImageIds.split(',') : [];
            console.log('operatingImageIdsText======',operatingImageIdsText)
            return {
              ...item,
              taskTypeText: this.getEnumText(item.taskType, getTaskType()),
              lastDisposeTypeText: this.getEnumText(
                item.lastDisposeType,
                getTasksLastDisposeTypeArr
              ),
              lastDisponseTypeColor: this.getEnumColor(
                item.lastDisposeType,
                getTasksLastDisposeTypeArr
              ),
              receiveUserNames:receiveUserIds.map(item => item.recordName).join(','),
              priorityText: this.getEnumText(item.priority, getPriorityArr),
              priorityColor: this.getEnumColor(item.priority, getPriorityArr),
              incentiveFeePriceText: item.incentiveFeePrice
                ? +this.$df.divide(+item.incentiveFeePrice, 100)
                : 0,
              payChannelText: this.getEnumText(
                item.payChannel,
                getPayChannel()
              ),
              exLoading: false,
              receiveUserList: receiveUserIds,
              auditStatusText: this.getEnumText(item.auditStatus,getDmCommonAuditStatus()),
              operatingImageIdsText: operatingImageIdsText,
              activityInviteFinishStatusText: this.getEnumText(item.activityInviteFinishStatus, getActivityInviteFinishStatusList())
            };
          });
          this.tableData = res.data;
        })
        .then((res) => {
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.cell-img{
  width: 50px;
  height: 50px;
  margin-right: 10px;
}
.hidden-box {
  height: 0;
  overflow: hidden;
}
.exportLoadingBox {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}
.btn {
  margin-bottom: 10px;
}
::v-deep .content .search {
  min-height: 0px;
  .main {
    display: flex;
    width: 350px;
    margin-right: 10px;
    .span {
      min-width: 125px;
      line-height: 40px;
    }
  }
}
</style>
