/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 通知管理-通知列表
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/notice/delete/batch/${data.ids}`);
}

// 根据id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/notice/delete/one/${data.id}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/notice/insert`, data)
}

// 更新上下架
export function pushstatusUpdate (data) {
    return requestV1.putForm(`${prefix}/notice/pushstatus/update`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/notice/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/notice/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/notice/query/page`, data)
}

// 更新置顶状态
export function topstatusUpdate (data) {
    return requestV1.get(`${prefix}/notice/topstatus/update`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/notice/update`, data)
}
