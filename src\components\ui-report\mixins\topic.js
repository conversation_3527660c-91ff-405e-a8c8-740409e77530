import {
  initColumnar,
  initColumnarTwoChart,
  initColumnarFourChart,
} from "@/components/exportPDF/template/columnar.js";
const radioColorResult = [
  '#5b9bd5',
  '#ed7d31',
  '#a5a5a5',
  '#ffc000',
  '#4472c4',
  '#70ad47',
  '#255e91',
  '#9e480e',
  '#636363',
  '#997300',
  '#264478',
  '#43682b',
  '#7cafdd',
  '#f1975a',
  '#b7b7b7',
  '#ffcd33',
  '#698ed0',
  '#8cc168',
  '#327dc2',
  '#d26012',
  '#848484',
  '#cc9a00',
  '#335aa1',
  '#5a8a39',
  '#9dc3e6',
  '#f4b183',
  '#c9c9c9',
  '#ffd966',
  '#8faadc',
  '#a9d18e',
  '#1f4e79',
  '#843c0b'
]
export default {
  data() {
    return {
      saTime: 1000,
      uuid: null,
    }
  },
  methods: {
    getTargetText(text,end = 6) {
      let str = text.substr(0, end);
      let temp = text.substr(end);
      while (temp.length != 0) {
        let t = temp.substr(0, end);
        temp = temp.substr(end);
        str += "\n" + t;
      }
      return str;
    },
    getParametricEquationTwo(startRatio, endRatio, isSelected, isHovered, k) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;

      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },

        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },

        x: function (u, v) {
          if (u < startRadian) {
            return (
              offsetX +
              Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y: function (u, v) {
          if (u < startRadian) {
            return (
              offsetY +
              Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          if (u > endRadian) {
            return (
              offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            );
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u);
          }
          return Math.sin(v) > 0 ? 1 : -1;
        },
      };
    },
    getPie3DTwo(pieData, internalDiameterRatio) {
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let k =
        typeof internalDiameterRatio !== "undefined"
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
          name:
            typeof pieData[i].name === "undefined"
              ? `series${i}`
              : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
        };

        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};

          typeof pieData[i].itemStyle.color != "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;

          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;

        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquationTwo(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          true,
          false,
          1
        );

        startValue = endValue;

        legendData.push(series[i].name);
      }
      console.log("legendData", legendData);

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //animation: false,
        legend: {
          show: true,
          data: legendData,
          type: "plain",
          // orient: "vertical",
          left: 0,
          top: 265,
          // bottom: 6,
          textStyle: {
            // color: "#CAEFFF",
            color: "#000",
          },
          itemGap: 10,
        },

        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1.3,
          max: 1.3,
        },

        grid3D: {
          show: false,
          height: 350,
          width: 350,
          boxHeight: 20, // 饼图厚度
          // left: "5%",
          // top: "0%",
          top: "-20%",
          left: "-3%",
          viewControl: {
            // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25,
            distance: 300, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 控制是否自动旋转
            //   autoRotateSpeed: 5,
            //   autoRotateAfterStill: 10
          },
        },
        series: series,
      };
      return option;
    },
    getTargetText(text = '', start = 6) {
      let str = text.substr(0, start);
      let temp = text.substr(start);
      while (temp.length != 0) {
        let t = temp.substr(0, start);
        temp = temp.substr(start);
        str += "\n" + t;
      }
      return str;
    },
    initProbleDataChart(item, idx, strUUID, totalParams = {}) {
      return new Promise((resolve, reject) => {
        //1 实例化对象
        let myCharts4 = window.echarts.init(document.querySelector(strUUID));
        const colorResult = [
          "rgba(127, 181, 255,0.6)",
          "rgba(166, 228, 239,0.6)",
          "rgba(188, 161, 254,0.6)",
          "rgba(103, 96, 254,0.6)",

          "rgba(232, 177, 0,0.6)",
          "rgba(2, 170, 254,0.6)",
          "rgba(129, 78, 204,0.6)",
          "rgba(203, 130, 81,0.6)",
          "rgba(163, 200, 78,0.6)",
          "rgba(23, 179, 103,0.6)",
        ];
        let tidx = idx;
        for (let i = 0; i < item.seriesData.length; i++) {
          let color = "";
          if (!colorResult[tidx]) {
            tidx = 0;
          }
          color = colorResult[tidx];
          tidx += 1;
          item.seriesData[i].itemStyle = {
            color: color,
            opacity: 0.6,
          };
        }
        let timer = this.saTime;
        let belongType = item.belongType;
        if (belongType === 4) {
          //3 把配置给实例对象
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 3);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          console.log("4");
          let max = Math.max.apply(null, optionData);
          let min = Math.min.apply(null, optionData);
          let eNum = 100;
          if (max < 100) {
            eNum = max + min;
          }
          initColumnarFourChart(myCharts4, optionData, yAxisData, eNum, {
            backgroundColor: "#fff",
            ...totalParams
          });
          setTimeout(() => {
            resolve(true);
          }, timer);
        } else if (belongType === 2) {
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 10);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          console.log("2");
          initColumnar(myCharts4, optionData, yAxisData, totalParams);
          setTimeout(() => {
            resolve(true);
          }, timer);
        } else if (belongType === 3) {
          let yAxisData = [];
          let optionData = [];
          for (let k = 0; k < item.seriesData.length; k++) {
            let text = this.getTargetText(item.seriesData[k].name, 3);
            yAxisData.unshift(text);
            optionData.unshift(item.seriesData[k].value);
          }
          initColumnarTwoChart(myCharts4, optionData, yAxisData);
          console.log("3");
          setTimeout(() => {
            resolve(true);
          }, timer);
        } else if (belongType === 1) {
          // 传入数据生成 option
          let option = this.getPie3DTwo(item.seriesData, 0.59);
          // 绘制图表
          myCharts4.setOption(option);
          console.log("1");
          setTimeout(() => {
            resolve(true);
          }, timer);
        }
      });
    },
    // 初始化echart 饼图
    async initRadioChart2D(item, strUUID, totalParams = {}) {
      let idx = 0;
      console.log('item.seriesData=========', item.seriesData)
      let idCount = 100;
      let {
        opacity = 1,
        userColorResult = [],
        openUserColor = false,
        labelOption = {},
        seriesOption = {},
        legendOption = {}
      } = totalParams
      for (let i = 0; i < item.seriesData.length; i++) {
        let color = radioColorResult[idx];
        if(openUserColor && userColorResult[idx]) {
          color = userColorResult[idx]
        }
        idx += 1;
        item.seriesData[i].itemStyle = {
          color: color,
          opacity: opacity
        };
        // item.seriesData[i].id = idCount;
        item.seriesData[i].name = idCount + '&&' + item.seriesData[i].name;
        idCount += 1;
      }
      let option = {
        tooltip: {
          show: false,
        },
        legend: {
          left: 'center',
          formatter: function (name) {
            return name.split('&&').slice(1).join('')
          },
          ...legendOption,
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: item.seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: function (params) {
                let citem = item.seriesData.find(item => item.name === params.name);
                return params.name.split('&&').slice(1).join('') + (citem ? '(' + citem.selectOptionProportion + ')' : '')
              },
              ...labelOption,
            },
            ...seriesOption,
          }
        ]
      };
      let myChart = window.echarts.init(document.querySelector(strUUID));
      myChart.setOption(option, true);
    },
    // 初始化柱状图
    async initBarChart2D(item, strUUID, totalParams = {}) {
      let idx = 0;
      let idCount = 100;
      let {
        color = '#fff28e',
        xAxisOption = {},
        yAxisOption = {},
        userOption = {},
        labelOption = {},
        seriesOption = {},
      } = totalParams
      let categoryResult = [];
      let valueResult = [];
      for (let i = 0; i < item.seriesData.length; i++) {
        let vitem = item.seriesData[i];
        categoryResult.push(vitem.name);
        valueResult.push(vitem.value);
      }
      let option = {
        xAxis: {
          type: 'category',
          data: categoryResult,
          ...xAxisOption
        },
        yAxis: {
          type: 'value',
          ...yAxisOption
        },
        series: [
          {
            data: valueResult,
            type: 'bar',
            barWidth: 24,
            itemStyle: {
              borderRadius: [10, 10, 0, 0],
              color: color
            },
            label: {
              show: true,
              position: 'top',
              ...labelOption
            },
            ...seriesOption
          }
        ],
        ...userOption,
      };

      let myChart = window.echarts.init(document.querySelector(strUUID));
      myChart.setOption(option, true);
    },
    async initBarChart2DV2(item, strUUID, totalParams = {}) {
      let idx = 0;
      let idCount = 100;
      let {
        color = '#fff28e',
        xAxisOption = {},
        yAxisOption = {},
        gridOption = {},
        seriesOption = {},
        labelOption = {},
        barWidth = 24,
        itemStyleOption = {}
      } = totalParams
      let categoryResult = [];
      let valueResult = [];
      for (let i = 0; i < item.seriesData.length; i++) {
        let vitem = item.seriesData[i];
        categoryResult.push(vitem.name);
        valueResult.push(vitem.value);
      }
      let option = {
        xAxis: {
          type: 'value',
          ...xAxisOption
        },
        yAxis: {
          type: 'category',
          data: categoryResult,
          ...yAxisOption
        },
        series: [
          {
            data: valueResult,
            type: 'bar',
            barWidth: barWidth,
            itemStyle: {
              borderRadius: [5, 5, 5, 5],
              color: color,
              ...itemStyleOption
            },
            label: {
              show: true,
              position: 'right',
              ...labelOption,
            },
            ...seriesOption
          }
        ],
        grid: {
          ...gridOption
        },
      };

      let myChart = window.echarts.init(document.querySelector(strUUID));
      myChart.setOption(option, true);
    }
  }
}