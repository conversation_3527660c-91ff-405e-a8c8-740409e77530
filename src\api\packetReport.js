/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api'
    //渗透率-(已禁塑和含未禁塑)
export function queryMonthPermeateRateDistributionStatList(data) {
    return requestV1.get(prefix + '/packetReport/queryMonthPermeateRateDistributionStatList', data);
}

//不同医院渗透率分布情况
export function getPermeateRateDistributionStat(data) {
    return requestV1.get(prefix + '/packetReport/getPermeateRateDistributionStat', data);
}

//渗透率-(已禁塑和含未禁塑)
export function queryHospitalPacketPermeateRateStat(data) {
    return requestV1.postJson(prefix + '/packetReport/queryHospitalPacketPermeateRateStat', data);

}

//查询医院月日均出袋量与渗透率
export function queryHospitalPacketPermeateRateStatList(data) {
    return requestV1.postJson(prefix + '/packetReport/queryHospitalPacketPermeateRateStatList', data);
}

//月度出袋量数据分析统计
export function queryMonthOutPacketStatList(data) {
    return requestV1.postJson(prefix + '/packetReport/queryMonthOutPacketStatList', data);
}

//单院日均出袋量数据分析统计
export function queryHospitalDayAvgOutPacketStat(data) {
    return requestV1.postJson(prefix + '/packetReport/queryHospitalDayAvgOutPacketStat', data);
}
