import env from '@/config/env'
import requestV1 from '@/common/utils/modules/request'

let prefix = '/adservice/api/v1'

// 批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/shorturlaccesslog/delete/batch/${data.ids}`)
}

// 根据id指定删除
export function add(data) {
    return requestV1.deleteForm(prefix + `/shorturlaccesslog/delete/one/${data.id}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(prefix + `/shorturlaccesslog/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(prefix + `/shorturlaccesslog/query/list`, data)
}

// 根据id单一查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/shorturlaccesslog/query/one`, data)
}

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/shorturlaccesslog/query/page`, data)
}

// 统计分页
export function queryStatisticPage (data) {
    return requestV1.postJson(`${prefix}/shorturlaccesslog/query/statistic/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/shorturlaccesslog/update`, data)
}

// 统计数据
export function getStatistic (data) {
    return requestV1.get(`${prefix}/shorturlaccesslog/get/statistic`, data)
}