<template>
  <div class="information-page-v6">
    <template v-for="page in pageContent">
      <div
        class="information-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="information-top"></div>
        <div class="information-t">{{ subTitle }}</div>
        <div class="information-c">
          <div class="information-base">
            <div class="information-base-item">
              <div class="information-base-l">
                <img
                  :src="filePrex + serviceIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">服务商：</span>
              </div>
              <div class="information-base-r">
                {{ pageObject.serviceProvider }}
              </div>
            </div>
            <div class="information-base-item noBottom">
              <div class="information-base-l">
                <img
                  :src="filePrex + projectIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">项目方：</span>
              </div>
              <div class="information-base-r">
                {{ pageObject.projectParty }}
              </div>
            </div>
          </div>
          <div class="information-base-item i-v2">
            <div class="information-base-l">
              <img
                :src="filePrex + companyIco"
                class="information-base-icon"
                alt=""
              />
              <span class="bold">需求名称：</span>
            </div>
            <div class="information-base-r">{{ pageObject.ctitle }}</div>
          </div>
          <div class="information-base">
            <div class="information-base-item">
              <div class="information-base-l">
                <img
                  :src="filePrex + locationIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">需求地点：</span>
              </div>
              <div class="information-base-r">
                {{ pageObject.demandLocation }}
              </div>
            </div>
            <div class="information-base-item">
              <div class="information-base-l">
                <img
                  :src="filePrex + timeIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">执行日期：</span>
              </div>
              <div class="information-base-r">{{ pageObject.monthText }}</div>
            </div>
            <div class="information-base-item-v2 noBottom">
              <img
                :src="filePrex + descIco"
                class="information-base-icon"
                alt=""
              />
              <span class="bold">需求描述：</span>
              {{ pageObject.demandDescribe }}
            </div>
          </div>
          <div class="information-base-v2">
            <div class="information-base-item noBottom">
              <div class="information-base-l">
                <img
                  :src="filePrex + numbersIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">项目执行人数：</span>
              </div>
              <div class="information-base-r">
                {{ pageObject.taskUserNum || 0 }}人
              </div>
            </div>
            <div class="information-base-item noBottom">
              <div class="information-base-l">
                <img
                  :src="filePrex + dateIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">数据收集数量：</span>
              </div>
              <div class="information-base-r">
                {{ pageObject.pharmacyNumber }}条
              </div>
            </div>
          </div>

          <template v-if="pagetype === 4">
            <div class="information-base">
              <div class="information-base-item noBottom">
                <div class="information-base-l">
                  <img
                    :src="filePrex + time2Ico"
                    class="information-base-icon"
                    alt=""
                  />
                  <span class="bold">数据提交时间：</span>
                </div>
                <div class="information-base-r">
                  {{ pageObject.createTimeText }}
                </div>
              </div>
            </div>
            <div class="information-base-v2">
              <div class="information-base-item-v2 noBottom">
                <img
                  :src="filePrex + numbersIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">拜访客户：</span>
                {{ pageObject.visitCustomer }}
              </div>
            </div>
            <div class="information-base-v2 npt">
              <div class="information-base-item-v2 noBottom">
                <img
                  :src="filePrex + dateIco"
                  class="information-base-icon"
                  alt=""
                />
                <span class="bold">拜访终端：</span>
                {{ pageObject.visitTerminal }}
              </div>
            </div>
          </template>
        </div>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      subTitle: "",
      filePrex:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/",
      serviceIco: "icon-service.png",
      projectIco: "icon-project.png",
      companyIco: "icon-company.png",
      locationIco: "icon-location.png",
      timeIco: "icon-time.png",
      descIco: "icon-desc.png",
      numbersIco: "icon-numbers.png",
      time2Ico: "icon-time-two.png",
      dateIco: "icon-data.png",
      pageContent: [
        {
          type: "information-page",
          uuid: "information-page_0",
          children: [],
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
.information-page-v6 {
  background: #fff;
  .information-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 88px 47px 20px 48px;
  }
  .information-top {
    height: 88px;
    width: 100%;
    background: var(--backgroud-top-bg-v3);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 10% 0%;
  }
  .information-t {
    margin-top: 32px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 16px;
    background-size: 100% 100%;
  }
  .information-base {
    background: #f0f0f0;
    padding: 21px 19px;
  }
  .information-base-v2 {
    padding: 21px 19px;
  }
  .information-base-v2.npt {
    padding-top: 0;
  }
  .information-base-item {
    display: flex;
    align-items: center;
    margin-bottom: 19px;
    flex: 1;
  }
  .information-base-item-v2 {
    margin-bottom: 19px;
    line-height: 2;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
  }
  .information-base-item.noBottom {
    margin-bottom: 0;
  }
  .information-base-item-v2.noBottom {
    margin-bottom: 0;
  }
  .information-base-item.i-v2 {
    margin: 16px 19px;
  }
  .information-base-l {
    line-height: 2;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
    display: flex;
    align-items: center;
  }
  .information-base-r {
    line-height: 2;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
  }
  .information-base-icon {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    margin-right: 8px;
  }
  .information-base-v2 {
    display: flex;
  }
  .bold {
    font-weight: 550;
  }
}
</style>