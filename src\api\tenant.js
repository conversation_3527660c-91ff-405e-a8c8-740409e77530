import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//更新租户角色
export function updateTenantRoles(data) {
  return requestV1.putJson(prefix+'/tenant/updateTenantRoles', data);
}

//获取租户的所有角色
export function listTenantRoles(data) {
  return requestV1.get(prefix+'/tenant/listTenantRoles', data);
}

//获取所有租户分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix+'/tenant/queryPage', data);
}

//获取所有租户
export function listAllTenant(data) {
  return requestV1.get(prefix+'/tenant/listAllTenant', data);
}

//删除租户id
export function deleteTenant(data) {
  return requestV1.deleteForm(prefix+'/tenant/deleteTenant', data);
}

//添加租户id
export function addTenant(data) {
  return requestV1.postJson(prefix+'/tenant/addTenant', data);
}

// 获取全部租户
export function queryAllList(data) {
  return requestV1.get('/auth/api/v1/tenant/query/all/list', data);
}
