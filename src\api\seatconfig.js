import requestV1 from '@/common/utils/modules/request'

const prefix = '/im/api/v1'

/**
 * 坐席管理
 */

// 分页查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/seatconfig/query/page`, data);
}

// 保存数据 新增
export function insert (data) {
    return requestV1.postJson(`${prefix}/seatconfig/insert`, data);
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/seatconfig/update`, data);
}

// 根据id查询
export function queryOne (data) {
    return requestV1.get(`${prefix}/seatconfig/query/one`, data);
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/seatconfig/query/list`, data);
}

// 获取坐席托管医生列表
export function seatconfigPhysicianList (data) {
    return requestV1.get(`${prefix}/seatconfig/physician/list`, data)
}
