import requestV1 from '@/common/utils/modules/request'

let prefix = '/adservice/api/v1'
    //分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/putplan/query/page', data);
}

//根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/putplan/query/list', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/putplan/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/putplan/update', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/putplan/query/one', data);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/putplan/delete/batch/${data}`);
}

//查询计划绑定或未绑定策略
export function queryPlanTacticsPage(data) {
    return requestV1.postJson(prefix + '/putplan/queryPlanTacticsPage', data);
}

//批量绑定策略
export function batchBindTratics(data) {
    return requestV1.putJson(prefix + '/putplantactics/batchBindTratics', data);
}

//批量解绑策略
export function batchUnboundTratics(data) {
    return requestV1.putJson(prefix + '/putplantactics/batchUnboundTratics', data);
}

//更投放计划状态
export function updateExtendStatus(data) {
    return requestV1.putJson(prefix + '/putplan/updateExtendStatus', data);
}