/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//测试出袋querypage
export function queryTestPage(data) {
    return requestV1.postJson(prefix+'/packetRecord/queryTestPage', data);
}

//测试出袋querypage
export function queryPage(data) {
    return requestV1.postJson(prefix+'/packetRecord/queryPage', data);
}

// 领袋异常报表
export function queryDevicePacketStatusReport (data) {
    return requestV1.get('/export/api/packetRecord/queryDevicePacketStatusReport', data)
}
