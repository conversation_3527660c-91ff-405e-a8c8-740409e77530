<template>
  <el-drawer
    :title="showTitleMap[showType]"
    :visible.sync="dialogVisible"
    size="1000px"
    :before-close="handleClose"
    center
  >
    <searchList
      v-if="showType !== 'serviceRecord' && !isTabList"
      :from-data="currentOptions"
      :config="{ size: 24, labelWidth: '150px' }"
      @selectVal="selectVal"
      @getImgUrlObj="getImgUrl"
    >
        <template slot="bookName" slot-scope="{ row }">
          <el-button @click="()=>dialogBookName = true">{{row.value || '请选择就诊人'}}</el-button>
        </template>
        <template slot="posterImage" slot-scope="{ row }">
          <img v-if="row.value" :style="{width:row.width + 'px',height:row.height + 'px'}" :src="row.value" alt="图片"  />
        </template>
        <template slot="tag" slot-scope="{ row }">
          <el-alert :type="row.tagType" class="warningPadding">
            <template slot="title">
              {{row.WarningTip}}
            </template>
          </el-alert>
        </template>
        <template slot="showJoinOrder">
          <div>
            <p>合计￥{{joinOrderPayPrice / 100}}</p>
            <div class="joinOrderShowMap">
              <div class="joinOrderShowMapItem" :key="item.id" v-for="(item,index) in accompanyOrderDTOList">
              <div class="orderItemTitle">订单{{index + 1}}</div>
              <div>
                <p>订单号：{{item.id}}</p>
                <p>就诊医院：{{item.hospitalName}}</p>
                <p>陪诊时间：{{item.startTime}}~{{item.endTime}}</p>
                <p>服务：{{item.serviceName}}</p>
                <p>服务价格：{{item.payPrice / 100}}</p>
              </div>
            </div>
            </div>
          </div>
        </template>
        <!-- S 联合订单 -->
        <template slot="accompanyOrderDTOList" slot-scope="{ row }">
          <div>
            <div v-for="(item, index) in row.value" :key="index" ref="jointOrder">
              <div class="jointOrderHeader">
                <p>订单{{index + 1}}</p>
                <div>
                  <el-button type="primary" size="mini" @click="packUp(item)">{{item.isPackUp ? '展开' : '收起'}}</el-button>
                  <el-button v-if="row.value.length > 1" type="danger" size="mini" @click="delMode(row.value,index)" >删除</el-button>
                </div>
              </div>
              <!-- 表单实体 -->
              <div class="optionsOrder" :class="{packUp:item.isPackUp}">
                <searchList @selectVal="selectOptionsOrderVal" :from-data="item.list" :config="{ size: 24, labelWidth: '170px', groupId: 27000 }">
                </searchList>
              </div>
            </div>
            <el-button type="primary" size="mini" @click="addMode">添加订单</el-button>
          </div>
        </template>
        <!-- E 联合订单 -->
       <div slot="btnList">
      <div class="custom-table-spacing">
      <!-- 医患权限控制 -->
        <patient-record-selector
          v-if="showType === 'dispatcher' || showType === 'againDispatcher'"
          :paramsData="paramsData"
          :providerId="providerId"
          :initValue="selectedOption"
          @optionChanged="handleOptionChanged"
          @selectionChange="handleSelectionChange"
          @serviceRecord="handleServiceRecord"
          @getImgUrl="getImgUrl"
          ref="patientRecordSelector"
        />
        </div>
      </div>
    </searchList>
    <div v-if="showType === 'serviceRecord'">
      <div class="btn-group">
        <el-button type="success" size="mini" @click="addSignIn">新增</el-button>
        <!-- <el-button type="danger" size="mini" @click="mulDel">批量删除</el-button> -->
      </div>
      <signInTab
        ref="signInTabRef"
        :tableData="signInRecordList"
        :loading="loading"
        @showTab="showTab"
        @select="select"
      />
      <add :show.sync="addShow" :paramsData="addParamsData" @closeAddSign="loadSignInRecordList" :orderId="this.paramsData.id"/>
      <servicerecord :show.sync="addindex" :paramsData="addParamsData" @closeAddSign="loadSignInRecordList" :orderId="this.paramsData.id"/>
    </div>
    <!-- 列表 -->
    <addTab :buttonMap="buttonMap" :currentOptions="currentOptions" @select="selectItem" :providerId="providerId" v-if="isTabList && visible"></addTab>
    <template v-if="showType !== 'serviceRecord' && buttonMap">
      <el-button class="buttonMap" v-for="(item) in buttonMap" :type="item.type" size="mini" @click="btnCentralized(item.id,item.apiFunc)">{{item.butName}}</el-button>
    </template>
    <!-- 选择就诊人弹窗 -->
    <el-dialog
      :modal="false"
      title="选择陪诊人"
      :visible.sync="dialogBookName"
      width="50%"
      :before-close="()=>dialogBookName = false"
      :append-to-body="true"
      center>
      <div class="orderListItem">
        <orderListItem :isUnit="true" :current-provider="currentProvider" @rowClick="selectPatient"></orderListItem>
      </div>
    </el-dialog>

    <!-- 服务记录弹窗为组件 -->
    <service-record-dialog
      :visible.sync="serviceRecordVisible"
      :orderId="serviceRecordOrderId"
    />
  </el-drawer>
</template>

<script>
import addTab from './addTab.vue';
import signInTab from './sign-in-tab'
import add from './add'
import servicerecord from './servicerecord'
import PatientRecordSelector from './PatientRecordSelector.vue'
import ServiceRecordDialog from './ServiceRecordDialog.vue'
import { Message } from 'element-ui'
import { getFromData ,showModal} from "@/utils/index";
import formMixin from "@/mixin/formMixin";
import options from './options.js';
import {accompanycombineorderQueryCombineOrder,getAccompanyserviceOne,getAccompanyproviderPage,getAccompanyemployeePage,accompanybookOrderCode,accompanyGetLogList,getAccompanyprovideruserQueryOne,accompanybookDeleteBatch, bindOfflineOrder, accompanyBookQueryRecordPage, accompanypatientrecordQuerySeeList,minichannellinkQueryOne,accompanypatientQueryListByPhone,accompanybookFinishOrderRefund} from "@/api/dmCommunity/accompany-doctor.js";
import {getShospitalQueryPage} from '@/api/dmCommunity/framerhospital.js'
import {crawlershospitaldeptQuery,crawlershospitaldoctor} from '@/api/hospitalMap.js'
import {getSource} from '@/views/accompany-doctor/getSource.js'
import orderListItem from '@/views/accompany-doctor/drug-order-listItem/index.vue'
import { insert, update, queryOne } from '@/api/dmCommunity/minichannellink'
import createPoster from './createPoster.js';
import { accompanyserviceclassifyQueryPage,getAccompanyServicePage} from '@/api/dmCommunity/accompany-doctor'
export default {
  mixins: [formMixin],
  inject:['getAuditStatusMap'],
  components: {
    signInTab,
    add,
    servicerecord,
    PatientRecordSelector,
    orderListItem,
    addTab,
    ServiceRecordDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showType:{
      type:String,
      default:''
    },
    providerId:{
      type:String,
      default:''
    },
    paramsData: {
      type: Object,
      default: () => {
        return null;
      },
    },
    tabIndex: {
        type: Number,
        default: 1,
    },
    patientInformation:{
      type:Object,
      default:null
    },
    currentProvider:{
      type:Object,
      default:null
    }
  },
  data() {
    return {
      file_ctx:this.$env.file_ctx,
      dialogVisible: false,
      defaultAppId:'wx436b8e65632f880f',
      currentOptions:null,
      showTitleMap:{},
      buttonMap:[],
      signInRecordList: [],
      selectArr:[],
      addShow:false,
      addindex:false,
      loadFlag:false, //判断是否已经加载了传递来的参数
      accompanyOrderDTOList:[],
      joinOrderPayPrice:0,
      selectedOption: 1,
      selectedRecordIds: [], // 已选择的陪诊记录ID集合
      dialogBookName:false,
      dto:{},
      isTabList:false,
      currentSelectItem:null,
      // 服务记录相关变量，保留必要的
      serviceRecordVisible: false,
      serviceRecordOrderId: null,
      allServices: [],
    };
  },
  async created() {
    // 初始化由PatientRecordSelector组件处理
  },
  computed: {
    mode(){
      return this.getFormData('mode','value')
    },
    provinces(){
      return this.getFormData('provinces','value')
    },


  },
  watch: {
    mode(n){
      console.log('auditStatus',n);
      if(n){
        if(n === 1){
          this.setFormData('employeeId','hidden',false)
        }else{
          this.setFormData('employeeId','hidden',true)
        }
      }
    },
    async provinces(n){
      this.upDataProvince(n)
    },
    async showType(){
      let showType = this.showType;
      console.log('paramsData',this.paramsData,showType,this.currentProvider);
      this.loadFlag = false
      let currentOptions = await options(showType,this.providerId,this.currentProvider,this.paramsData);
      this.currentOptions = currentOptions.options || this.currentOptions;
      let oldButtonMap = this.buttonMap;
      this.buttonMap = null;
      this.$nextTick(()=>{
        this.buttonMap = currentOptions.buttonMap || oldButtonMap;
      })
      this.showTitleMap = currentOptions.showTitleMap || this.showTitleMap;
      this.isTabList = currentOptions.isTabList;
      console.log('currentOptions',currentOptions);

      // 如果是创建订单模式，加载服务分类
      if(showType === 'add' || showType === 'cloneOrder') {
        await this.loadServiceClassify();
      }
      if(showType === 'add' || showType === 'cheateOrder' || showType === 'cloneOrder'){
        // 如果当前服务商云陪诊和独立小程序开关都没开则不允许创单
        if(!this.currentProvider.storeButton && !this.currentProvider.appidButton){
          this.$message.error('请开启云陪诊或独立小程序服务');
          return this.close(true);
        }
        // 如果当前服务商的appId不等于默认的appId,且服务商开启了云门店码和独立小程序码
        if(this.currentProvider.appid !== this.defaultAppId && this.currentProvider.storeButton && this.currentProvider.appidButton){
          this.setFormData('store', 'hidden', false)
        }
        // 如果当前服务商未开启云陪诊则说明是独立小程序
        if(!this.currentProvider.storeButton){
          this.setFormData('store', 'value', 0)
        }
      }
      // 判断是否开启人工导诊
      if(showType === 'ChangingReservation' && this.currentProvider.manualButton === 1){
        this.setFormData('deptName','hidden',false)
        this.setFormData('doctorName','hidden',false)
        this.setFormData('backupImg','hidden',false)
      }
      if(showType === 'orderCode'){
        let routerPath = 'modules/accompany-doctor/service-reservation/index';
        let codeId = this.paramsData.id;
        if(showType === 'orderCode' && this.currentProvider.combineButton === 1){
          // 判断为联合订单时则展示联合订单模块
          let combineOrderId = this.paramsData.combineOrderId;
          if(combineOrderId){
            // 判定是否为联合订单 如果是则切换路由
            routerPath = 'modules/accompany-doctor/service-reservation/joinOrder/joinOrder';
            this.joinOrderPayPrice = 0;
            codeId = combineOrderId;
            let {data:{accompanyOrderDTOList}} = await accompanycombineorderQueryCombineOrder({id:combineOrderId})
            accompanyOrderDTOList.map(async e=>{
              e.serviceName = (await getAccompanyserviceOne({id:e.serviceId}))?.data?.serviceName
              this.joinOrderPayPrice += e.payPrice;
            })
            this.accompanyOrderDTOList = accompanyOrderDTOList;
            this.setFormData('showJoinOrder','hidden',false)
          }
         }
         let codePath;
         let {data:{appid,poster,posterLogo,cAppid}} = await getAccompanyprovideruserQueryOne({id:this.providerId});
         // 如果该订单开启了云陪诊模式则说明是云小程序
         if(this.paramsData.store) appid = cAppid;
        // 查询该订单是否有二维码
        let {data:codeData} = await minichannellinkQueryOne({businessId:this.paramsData.id,businessType:4});
        // 如果有则直接返回
        if(codeData.qrcodePath){
          this.setFormData('orderCode','value',codeData.qrcodePath);
          codePath = codeData.qrcodePath;
        }else{
         // 如果没有则插入参数 渲染二维码
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          try{
            let {data} = await insert({
              appid,
              name:'订单二维码',
              businessType:4,
              businessId:codeId,
              path:routerPath + '?id=' + codeId,
              CustomParameters:`providerId=${this.providerId}`
            })
            this.setFormData('orderCode','value',data.qrcodePath);
            codePath = data.qrcodePath;
            loading.close();
          }catch(e){
            loading.close();
          }
        }
        let log = console.log;
        // 创建海报 需要 海报图路径 海报logo路径 二维码路径 用户名 服务名称 价格
        log('测试',poster && posterLogo,poster , posterLogo)
        if(poster && posterLogo){
          posterLogo = this.file_ctx + posterLogo;
          poster = this.file_ctx + poster;
          let {PosterBase64,width,height} = await createPoster({codePath,poster,posterLogo,bookName:this.paramsData.bookName,serviceName:this.paramsData.serviceName,payPrice:this.paramsData.payPrice})
          console.log('{posterBase64,width,height}',{PosterBase64,width,height});

          this.setFormData('posterImage','value',PosterBase64);
          this.setFormData('posterImage','width',width);
          this.setFormData('posterImage','height',height);
          PosterBase64 && this.setFormData('posterImage','hidden',false);
        }
      }
      // 取消订单时，在待派单、待接单、待服务状态下显示退款金额字段
      if(showType === 'cancelOrder'){
        // tabIndex: 3=待派单, 4=待接单, 5=待服务
        if(this.tabIndex >= 3 && this.tabIndex <= 5){
          this.setFormData('refundAmount','hidden',false)
        } else {
          this.setFormData('refundAmount','hidden',true)
        }
      }
      if(showType === 'cheateOrder'){
        this.setHospitalName(this.paramsData)
        console.log('this.paramsData11',this.paramsData);
        if(this.paramsData && this.paramsData.startTime) {
          // 等待DOM更新和组件初始化完成后再设置值
          setTimeout(() => {
            const startTime = this.paramsData.startTime;
            // 如果没有结束时间，使用固定的默认结束时间
            let endTime = this.paramsData.endTime;
            if(!endTime) {
              const endDate = new Date(startTime);
              endDate.setDate(endDate.getDate() + 1);
              endTime = this.$u.parseTime(endDate, '{y}-{m}-{d} {h}:{i}:{s}');
            }
            this.setFormData('startTimeMap', 'value', [startTime, endTime]);
          }, 100);
        }
      }
      // 服务记录
      if(showType === 'serviceRecord'){
        await this.loadSignInRecordList()
      }
      if(showType === 'changeOrder' && this.tabIndex === 1){
        this.setFormData('serviceId','hidden',true)
      }

      // 如果有城市信息，则根据城市获取医院列表
      if(this.paramsData && this.paramsData.city && this.paramsData.province) {
        console.log('根据城市获取医院列表', this.paramsData.city, this.paramsData.province);
        await this.setHospitalName({
          province: this.paramsData.province,
          city: this.paramsData.city
        });

        // 在获取完医院列表后检查是否有预设的医院名称
        if (this.paramsData.hospitalName) {
          const hospitalOptions = this.getFormData('hospitalName', 'option');
          console.log('当前医院选项:', hospitalOptions);

          // 如果找不到预设的医院，尝试将它添加到选项中
          if (hospitalOptions && Array.isArray(hospitalOptions)) {
            const exists = hospitalOptions.some(h => h.value === this.paramsData.hospitalName);
            if (!exists) {
              console.log('添加缺失的医院:', this.paramsData.hospitalName);

              // 添加缺失的医院到选项中
              hospitalOptions.push({
                label: this.paramsData.hospitalName,
                value: this.paramsData.hospitalName,
                hospitalName: this.paramsData.hospitalName
              });

              this.setFormData('hospitalName', 'option', hospitalOptions);
            }
          }
        }
      }

      if(this.paramsData){
        // 根据城市获取陪诊师列表
        const city = this.paramsData.city ? this.paramsData.city.replace(/市市辖区|市辖区|市/g, '') : '';
        const condition = {
          providerId: this.providerId,
          auditStatus: 2
        };

        // 只有在有城市信息时才添加城市筛选条件
        if (this.paramsData.city) {
          condition.city = this.paramsData.city;
        }

        let {data:{records}} = await getAccompanyemployeePage({
          size: 10000,
          condition: condition
        });

        records.map(e=>{
          e.label = e.username
          e.value = e.id
        });

        this.setFormData('employeeId','option',records);
      }
      if(!this.paramsData) return
      Object.keys(this.paramsData).map(id=>{
        if(id === 'backupImg'){
          let newBackValue = this.paramsData[id].split?.(',')
          if(this.paramsData[id] === ''){
            newBackValue = null;
          }
          this.setFormData(id,'value',newBackValue);
          return
        }
        // 退款金额需要从分转换为元显示
        if(id === 'refundAmount' && this.paramsData[id]){
          this.setFormData(id,'value',this.paramsData[id] / 100)
        }
        // 服务费需要从分转换为元显示
        else if(id === 'payPrice' && this.paramsData[id]){
          this.setFormData(id,'value',this.paramsData[id] / 100)
        } else {
          this.setFormData(id,'value',this.paramsData[id])
        }
      })
      if(this.getFormData("price","id")){
        console.log('this.getFormData("serviceId","option")',this.getFormData("serviceId","option"));
        console.log('this.getFormData("serviceId","value")',this.getFormData("serviceId","value"));
        let price = this.getFormData("serviceId","option").filter(e=>e.id === this.getFormData("serviceId","value"))[0]?.price / 100;
        price && this.setFormData('price','value',price);

        // 如果是创建服务单，使用订单中的价格
        if(this.showType === 'cheateOrder' && this.paramsData && this.paramsData.payPrice) {
          const orderPrice = this.paramsData.payPrice / 100;
          this.setFormData('price','value', orderPrice);
        }
      }
      // 处理派单和再次派单的情况
      if (showType === 'dispatcher' || showType === 'againDispatcher') {
        // 设置档案可见范围
        if (this.paramsData && this.paramsData.patientFileSee) {
          this.selectedOption = this.paramsData.patientFileSee;
        } else {
          this.selectedOption = 1; // 默认值
        }
      }

      setTimeout(()=>{
        this.loadFlag = true
      },100)
    },
    visible(n) {
      this.dialogVisible = n;
      if (!n) {
        this.formList.map((item) => {
          this.setFormData(item.id,'value', this.getFormData(item.id,'default') || null)
        });
      }else{
        console.log('this.paramsData',this.paramsData);
      }
    },
    paramsData(n){
      if(n){
      }else{

      }
    },
  },

  methods: {
    // 打开服务记录弹窗
    handleServiceRecord(row) {
      this.serviceRecordVisible = true;
      this.serviceRecordOrderId = row.id;
    },

    selectItem(val){
      this.currentSelectItem = val;
    },
    // 选择陪诊人
    selectPatient(row){
      let {name,phone,sex,idcard,age} = row;
      this.dto = {name,phone,sex,idcard,age};
      this.setFormData('bookName','value',name);
      this.setFormData('bookPhone','value',phone);
      console.log('选择陪诊人',row,this.currentOptions);

      if(this.patientInformList){
        this.patientInformList[0].value = name
        this.patientInformList[1].value = phone
        this.$emit('changePatientInformation', {bookName:name,bookPhone:phone})
      }
      // 选择结束后关掉弹窗
      this.dialogBookName = false;
    },
    // 联合订单响应模块
    selectOptionsOrderVal({val,item}){
      function getCurrentOptions(keyName) {
        let orderValue = this.getFormData("accompanyOrderDTOList", "value", true);
        let lists = orderValue.map(e=>e.list);
        let target,targetList;
        lists.map(options=>{
          options.map(option=>{
            if(option.id === keyName && option === item){
              target = option;
              targetList = options;
          }
          })
        })
        return {target,targetList}
      }
      let {target,targetList} = getCurrentOptions.call(this,item.id);
      // 服务分类
      if(item.id === 'classifyId'){
        // 根据分类ID筛选服务列表
        this.filterServicesByClassifyId(val, targetList);

        // 清空服务选择和价格
        const serviceItem = targetList.find(o => o.id === 'serviceId');
        if(serviceItem) {
          this.setFormData('serviceId', 'value', null, targetList);
          this.setFormData('price', 'value', null, targetList);
        }
      }
      // 服务
      if(item.id === 'serviceId'){
        // 检查是否选择了分类
        const classifyItem = targetList.find(o => o.id === 'classifyId');
        if(classifyItem && !classifyItem.value && val) {
          this.$message.warning('请先选择服务分类');
          this.setFormData('serviceId', 'value', null, targetList);
          return;
        }

        let price = target.option.filter(e=>e.id === val)[0]?.price / 100;
        this.setFormData('price','value',price,targetList);
      }
      // 地区
      if(item.id === 'provinces'){
        console.log(target,targetList);
        this.upDataProvince(target.value,targetList);
       }
      //  医院
       if(item.id === 'hospitalName'){
        let hospitalId = target.option.filter(e=>e.hospitalName === target.value)[0]?.id;
        this.setFormData('deptName','value',null,targetList);
        this.setFormData('doctorName','value',null,targetList);
        this.setdeptName(hospitalId,targetList)
       }
      //  科室
       if(item.id === 'deptName'){
        let deptId = target.option.filter(e=>e.name === target.value)[0]?.id;
        this.setFormData('doctorName','value',null,targetList);
        this.getDoctorName({deptId,hospitalId:this.currentHospitalId},targetList)
       }
    },
    upDataProvince(n,list=this.currentOptions){
      if(this.paramsData && this.loadFlag){
        this.setFormData('providerId','value',null,list);
        this.setFormData('initiateProviderId','value',null,list);
      }
      if(!n || n.length === 0) {
        this.setFormData('hospitalName','option',[],list);
        this.setFormData('providerId','option',[],list);
        this.setFormData('initiateProviderId','option',[],list);
        return
      }

      // 获取省市信息
      let {province,city} = {province:n[0],city:n[1]};
      console.log('更新省市:', {province, city});

      // 更新医院列表
      this.setHospitalName({province,city},list);

      // 增加传参数据保护逻辑
      if (!this.loadFlag) return;  // 初始化时不执行清空操作

      // 清空相关字段的值
      this.setFormData('hospitalName','value',null,list);
      this.setFormData('deptName','value',null,list);
      this.setFormData('doctorName','value',null,list);

      // 更新陪诊师列表
      if (this.providerId) {
        this.updateEmployeeList(city, list);
      }
    },

    // 根据城市更新陪诊师列表
    async updateEmployeeList(city, list = this.currentOptions) {
      if (!city || !this.providerId) return;

      try {
        // 获取陪诊师列表
        const { data: { records } } = await getAccompanyemployeePage({
          size: 10000,
          condition: {
            providerId: this.providerId,
            city: city,
            auditStatus: 2
          }
        });

        const employees = records.map(e => ({
          label: e.username,
          value: e.id
        }));
        this.setFormData('employeeId', 'option', employees, list);
      } catch (error) {
        console.error('更新陪诊师列表失败:', error);
      }
    },
    packUp(item){
      item.isPackUp = !item.isPackUp;
    },
    delMode(list,index){
      list.splice(index,1);
    },
    addMode(){
      let orderValue = this.getFormData("accompanyOrderDTOList", "value");
      orderValue.push({
        list:this.getOrderItem(orderValue[0]),
        isPackUp:false,
      });
      this.setFormData("accompanyOrderDTOList", "value", orderValue);

    },
    getOrderItem(item){
      return item.list.map(e=>{
        return {
          ...e,
          value:Array.isArray(e.value)? [] : null,
        }
      })
    },
    async loadSignInRecordList(){
      console.log('点击关闭');

      let {data} = await accompanyGetLogList({
        businessId:this.paramsData.id,
        businessType:'4'
      })
      this.signInRecordList = data.map(e=>{
        let clockTitleMap = {'1':'签到打卡','2':'签出打卡', '4':'接到客户打卡'}
        e.typeText = clockTitleMap[e.type.toString()]
        return e
      });
    },
    selectVal({item}){
      console.log('item',item);

      if(item.id === 'hospitalName'){
        // 查找医院ID
        let hospitalOption = item.option.find(e => e.hospitalName === item.value || e.value === item.value);
        let hospitalId = hospitalOption?.id;

        if (!hospitalId && item.value) {
          console.warn('找不到医院ID:', item.value);
          // 如果找不到ID但有医院名称，将医院添加到选项中
          item.option.push({
            id: 'custom_' + Date.now(),
            hospitalName: item.value,
            label: item.value,
            value: item.value
          });
          hospitalId = 'custom_' + Date.now();
        }

        this.currentHospitalId = hospitalId;
        console.log('选择医院:', item.value, '医院ID:', hospitalId);

        // 清空科室和医生
        this.setFormData('deptName','value',null);
        this.setFormData('doctorName','value',null);

        // 获取科室列表
        if (hospitalId) {
          this.setdeptName(hospitalId);
        } else {
          console.warn('医院ID为空，无法获取科室列表');
        }
      }
      if(item.id === 'deptName'){
        let deptId = item.option.filter(e=>e.name === item.value)[0]?.id;
        this.setFormData('doctorName','value',null);
        this.getDoctorName({deptId,hospitalId:this.currentHospitalId})
      }
      if(item.id === "serviceId"){
        // 检查是否选择了分类
        const classifyId = this.getFormData('classifyId', 'value');
        if(!classifyId && item.value) {
          this.$message.warning('请先选择服务分类');
          this.setFormData('serviceId', 'value', null);
          return;
        }

        let price = item.option.filter(e=>e.id === item.value)[0]?.price / 100;
        this.setFormData('price','value',price);
      }
      // 处理服务分类选择
      if(item.id === "classifyId"){
        // 根据分类ID筛选服务列表
        this.filterServicesByClassifyId(item.value);

        // 清空服务选择和价格
        this.setFormData('serviceId', 'value', null);
        this.setFormData('price', 'value', null);
      }
    },
    async setHospitalName({province,city},list=this.currentOptions){
      if (!province || !city) {
        console.warn('没有提供省份或城市信息，无法筛选医院');
        return;
      }
      try {
        // 去掉字符串里的省、市和市辖区等字样
        const formattedProvince = province.replace(/省|市/g,'');
        const formattedCity = city.replace(/市市辖区|市辖区|市/g,'');

        console.log('筛选医院参数:', {province: formattedProvince, city: formattedCity});

        let {data:{records:hospitalQuery}} = await getShospitalQueryPage({
          current:0,
          size:1000,
          condition:{
            province: formattedProvince,
            city: formattedCity,
          }
        });

        // 如果找不到医院，尝试只按省份筛选
        if (hospitalQuery.length === 0) {
          ({data:{records:hospitalQuery}} = await getShospitalQueryPage({
            current:0,
            size:1000,
            condition:{
              province: formattedProvince
            }
          }));
        }

        // 确保所有返回的医院都有正确的label和value属性
        hospitalQuery = hospitalQuery.map(e => {
          return {
            ...e,
            label: e.hospitalName || e.label,
            value: e.hospitalName || e.value
          }
        });
        // 不再需要更新searchOptions，因为它已被移到PatientRecordSelector
        this.setFormData('hospitalName','option',hospitalQuery,list);
      } catch (error) {
        console.error('获取医院列表失败:', error);
      }
    },
    async setdeptName(id,list=this.currentOptions){
      let {data:getdeptNameMap} = await crawlershospitaldeptQuery({id})
        let deptNameQuery = getdeptNameMap.map(e=>{
          return {...e,label:e?.name,value:e?.name}
        })
      this.setFormData('deptName','option',deptNameQuery,list);
    },
    async getDoctorName({hospitalId,deptId},list=this.currentOptions){
        let {data:getDoctorNameMap} = await crawlershospitaldoctor({hospitalId,deptId});
        console.log('getDoctorNameMap',getDoctorNameMap);
        let doctorNameQuery = getDoctorNameMap.map(e=>{
          return {...e,label:e.name,value:e.name}
        })
      this.setFormData('doctorName','option',doctorNameQuery,list);

      },
      mulDel() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一条数据");
      }

      const ids = this.selectArr.map((item) => {
        return item.id;
      });

      // deleteBatch
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          // this.close();
          accompanybookDeleteBatch(ids.join(',')).then(res => {
            this.$eltool.successMsg(res.msg);
            this.loadSignInRecordList();
          })
        })
        .catch((_) => {});
    },
    addSignIn() {
      this.addShow = true
      this.addParamsData = null
    },
      select(val) {
        this.selectArr = val;
      },
      showTab({ type, row }) {
      switch (type) {
        case 1:
          // 编辑
          this.addParamsData = row
          this.addShow = true
          break;
        case 2:
          // 删除
          this.$confirm('是否确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then( async () => {
            const res = await signinlogDeleteOne({id: row.id})
            this.$eltool.successMsg(res.msg)
          })
          break
        default:
      }
    },
    getImgUrl({ url, formData }) {
      this.setFormData('backupImg', 'value',url)
    },
    async bindLKLOrderItem(){
      if(!this.currentSelectItem) {
        return this.$eltool.errorMsg('请选择订单')
      }
      let confirmText = '请确认要绑定的拉卡拉订单号是否正确，如绑定错误将导致退款异常';
      let combineOrder = !!this.paramsData.combineOrderId;
      let id = this.paramsData.id;
      if(combineOrder) {
        id = this.paramsData.combineOrderId;
        const loading = this.$loading({ lock: true, text: '查询中', spinner: 'el-icon-loading', background: 'rgba(0, 0, 0, 0.7)'});
        let {data:{accompanyOrderDTOList}} = await accompanycombineorderQueryCombineOrder({id})
        loading.close();
        let sumPayPrice = 0;
        let orderText = '';
        accompanyOrderDTOList.map(e=>{
          sumPayPrice+=e.payPrice / 100;
          orderText += `${e.id}、`
        })
        orderText = orderText.slice(0,orderText.length-1);
        confirmText = `当前为联合订单（待支付价格：￥${sumPayPrice}），关联陪诊订单：（${orderText}）都将一并绑定，请确认要绑定的拉卡拉订单号是否正确，如绑定错误会将导致退款异常`
      }
      await this.$confirm(confirmText, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '绑定...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let {appid,logNo} = this.currentSelectItem;
      try {
        await bindOfflineOrder({id,combineOrder,logNo,appid})
      } catch (error) {
        loading.close();
      }
      loading.close();
      this.$eltool.successMsg('绑定成功')
      this.close(true);
    },
    async btnCentralized(butId, apiFunc) {
      switch (butId) {
        case 'clear':
          this.close(true);
          break;
        case 'bindLKLOrder':
          this.bindLKLOrderItem();
          break;
        // 创建联合订单上一步
        case 'showPatientInformation':
        this.patientInformList = [
                { title: '就诊人', type: 20, id: 'bookName',value:'请选择就诊人',must: true,domName:"el-button",onEvents:{
                    click: (event) => this.dialogBookName = true
                }},
                { title: '就诊人电话', type: 1, id: 'bookPhone',value:'',must: true},
              ]

          // 选择创建联合订单
          let patientInformation = await showModal({
              title:'创建联合订单',
              buttonTitle:'下一步',
              value: null,
              formList:this.patientInformList
          });
          this.patientInformList = null;
          return
          break;
        // 派单处理
        case 'dispatcher':
        case 'againDispatcher':
        // 获取PatientRecordSelector组件中的数据
        const recordData = this.$refs.patientRecordSelector.getData();
        if (recordData.selectedRecords.length === 0 && recordData.selectedOption === 1) {
          return this.$message.warning('请选择需要派单的记录');
        }
        // 初始化表单数据
        let formData = this.initFormData(this.currentOptions);
        if (!formData) return;
        // 添加基础数据
        const params = {
          id: this.paramsData.id,
          mode: formData.mode || this.getFormData('mode', 'value') || 1,
          providerId: this.providerId,
          providerName: this.currentProvider?.providerName || '',
          city: this.paramsData.city || '',
          province: this.paramsData.province || '',
          county: this.paramsData.county || '',
          employeeId: formData.employeeId || '',
          employeeName: '',
          remark: formData.remark || '',
          patientSeeType: recordData.selectedOption || 1,
          accompanyOrderIdList: []
        };

        // 如果是手动勾选模式(1)，添加选中的记录ID
        if (params.patientSeeType === 1) {
          params.accompanyOrderIdList = recordData.selectedRecords.map(item => item.id);

          // 确保有选中记录
          if (params.accompanyOrderIdList.length === 0) {
            return this.$message.warning('请选择需要添加的陪诊记录');
          }
        }

        // 如果选择了陪诊师，获取陪诊师名称
        if (params.employeeId) {
          const employeeList = this.getFormData('employeeId', 'option');
          const employee = employeeList?.find(e => e.value === params.employeeId);
          if (employee) {
            params.employeeName = employee.label || '';
          }
        }

        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在派单...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 调用派单API
          const res = await apiFunc(params);
          this.$message.success(res.msg || '派单成功');
          this.close(true);
        } catch (error) {
          console.error('派单失败:', error);
          this.$message.error(error.message || '派单失败，请稍后重试');
        } finally {
          loading.close();
        }
        break;
          // 查看就诊人信息
        case 'checkInfo':
        let Info = showModal({
              title:'查看就诊人信息',
              buttonTitle:'关闭',
              formConfig:{ size: 24, labelWidth: '150px' },
              check:false,
              showCancelButton:false,
              clearCallFlag:true,
              formList:[
                { title: "就诊人", id: "bookName", value: this.paramsData.bookName, type: 1,disable:true},
                { title: "就诊人电话", id: "bookPhone", value: this.paramsData.bookPhone, type: 1,option:[],disable:true},
                { title: "服务", id: "serviceId", value: this.paramsData.serviceName, type:1,disable:true},
                { title: "服务价格", id: "price", value: this.paramsData.price, type: 1,disable:true},
                { title: "就诊城市",id: "provinces", value: this.paramsData.provinces, type: 1,disable:true},
                { title: "就诊医院", id: "hospitalName", value: this.paramsData.hospitalName, type: 1,disable:true},
                { title: "医院科室", id: "deptName", value: this.paramsData.deptName, type: 1,disable:true},
                { title: "就诊医生", id: "doctorName", value: this.paramsData.doctorName, type: 1,disable:true},
                { title: "陪诊时间", id: "startTimeMap", value: this.paramsData.startTimeMap, type: 1,disable:true},
              ]
          });
          let domBox = Info.getDomBox();
          domBox.style.width = '600px';
          await Info;
          domBox.style.width = '';
          break;

        default:
          this.confirm(butId, apiFunc);
          break;
      }
    },
    setFormData(id, key, value,list=this.currentOptions) {
      if(!list) return
      for (let i = 0; i < list.length; i++) {
        if (list[i].id === id) {
          this.$set(list[i],key,value)
          return true;
        }
      }
    },
    getFormData(id,key,isJSON,list=this.currentOptions){
      if(isJSON && key) return (list?.filter(e=>e.id === id)[0] || {})?.[key]
      if(isJSON) return (list?.filter(e=>e.id === id)[0] || {})
      if(key) return JSON.parse(JSON.stringify(list?.filter(e=>e.id === id)[0] || {}))?.[key]
      return JSON.parse(JSON.stringify(list.filter(e=>e.id === id)[0] || {}))
    },
    initFormData(list){
      let formData = getFromData(list)
      if(!formData) return false;

      // 过滤掉不需要提交的字段
      list.forEach(item => {
        if(item.noSubmit && formData.hasOwnProperty(item.id)) {
          delete formData[item.id];
        }
      });

      if(formData.provinces && Array.isArray(formData.provinces)){
        formData.province = formData.provinces[0]
        formData.city = formData.provinces[1]
      }
      delete formData.provinces
      if(formData.backupImg && Array.isArray(formData.backupImg)){
        formData.backupImg = formData.backupImg.join(',')
      }
      if(formData.price){
        formData.price = formData.price * 100;
      }
      // 退款金额转换为分
      if(formData.refundAmount){
        formData.refundAmount = formData.refundAmount * 100;
      }
      return formData;
    },
    async confirm(butId,apiFunc) {
      const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
      });
      let formData = this.initFormData(this.currentOptions)
      if(!formData) return loading.close();

      formData.providerId = this.providerId;
      formData.source = await getSource(this.providerId);
      console.log('this.showType',this.showType,this.showType ==='CreationJointOrder');

      if(this.showType ==='CreationJointOrder'){
        formData.bookPhone = this.patientInformation.bookPhone;
        formData.bookName = this.patientInformation.bookName;
        formData.dto = this.dto;
        formData.accompanyOrderDTOList = JSON.parse(JSON.stringify(formData.accompanyOrderDTOList));
        let DTOlist = formData.accompanyOrderDTOList;
        for (let index = 0; index < DTOlist.length; index++) {
          const e = DTOlist[index];
          let miniFormData = this.initFormData(e.list);
          // 如果未通过数据校验则滑动到对应的表单
          if(!miniFormData) {
            loading.close();
            this.$refs.jointOrder[index].scrollIntoView({behavior: 'smooth'});
            Message.error(`请完善订单${index + 1}的信息`)
            return
          }
          miniFormData && (DTOlist[index] = miniFormData)
        }

      }
      if(this.showType === 'add'){
        formData.dto = this.dto;
        // 确保classifyId被包含在提交数据中
        const classifyId = this.getFormData('classifyId', 'value');
        if (classifyId) {
          formData.classifyId = classifyId;
        }
      }
      console.log('this.showType',this.showType,formData);

      if(this.showType === 'cloneOrder'){
        formData.dto = this.dto;
        if(Object.keys(this.dto).length === 0){
          let {data:ListByPhone} = await accompanypatientQueryListByPhone({
            providerId:this.providerId,
            phone:this.paramsData.bookPhone,
          })
          ListByPhone.map(e=>{
            if(e.name === this.paramsData.bookName){
              formData.dto = {idcard:e.idcard,name:e.name,phone:e.phone,sex:e.sex,age:e.age}
            }
          })
        }
        formData.cloneStatus = this.paramsData.orderState;
        // 确保classifyId被包含在提交数据中
        const classifyId = this.getFormData('classifyId', 'value');
        if (classifyId) {
          formData.classifyId = classifyId;
        }
      }
      try {
        await apiFunc(formData);
      }catch (error) {
        loading.close();
        return
      }
      console.log('formData',formData);
      loading.close();
      Message.success('插入成功')
      this.$emit("close", true);
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close(true);
        })
        .catch((_) => {});
    },
    close(type) {
      // 清空表格的勾选状态
      if (this.$refs.patientRecordSelector) {
        this.$refs.patientRecordSelector.clearSelection();
      }
      // 清空选中的记录ID集合
      this.selectedRecordIds = [];
      // 清空选中的行数据
      this.selectArr = [];
      this.$emit("close", type);
    },
    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    // 就诊人选中数据
    handleSelectionChange(selectedRows) {
      // 处理选中的行数据
      console.log('选中的行数据:', selectedRows);
      this.selectArr = selectedRows;
    },
    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      const second = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    // 编辑记录
    editRecord(row) {
      this.addShow = true;
      this.addParamsData = row;
    },
    // 删除记录
    deleteRecord(row) {
      this.$confirm('确认删除该记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里添加删除记录的API调用
      }).catch(() => {});
    },

    // 处理选项变化
    handleOptionChanged(val) {
      this.selectedOption = val;
    },
    async loadServiceClassify() {
      try {
        // 获取服务分类列表
        const res = await accompanyserviceclassifyQueryPage({
          current: 1,
          size: 1000,
          condition: {
            providerId: this.providerId
          }
        });

        if(res && res.code === 0 && res.data && res.data.records) {
          // 格式化分类数据作为下拉选项
          const classifyOptions = res.data.records.map(item => ({
            label: item.name,
            value: item.id,
            id: item.id
          }));

          // 设置分类下拉框选项
          this.setFormData('classifyId', 'option', classifyOptions);

          // 获取所有服务列表用于筛选
          this.allServices = JSON.parse(JSON.stringify(this.getFormData('serviceId', 'option')));
        }
      } catch (error) {
        console.error('获取服务分类列表失败', error);
        this.$message.error('获取服务分类列表失败');
      }
    },

    // 根据分类ID筛选服务列表
    async filterServicesByClassifyId(classifyId, list = this.currentOptions) {
      if (!classifyId) {
        // 如果没有选择分类，显示所有服务
        if (this.allServices) {
          this.setFormData('serviceId', 'option', this.allServices, list);
        }

        // 禁用服务选择
        const serviceItem = list ? list.find(item => item.id === 'serviceId') : this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', true);
        }

        return;
      }

      try {
        // 获取该分类下的服务
        const res = await getAccompanyServicePage({
          current: 1,
          size: 1000,
          condition: {
            classifyId,
            providerId: this.providerId
          }
        });

        if (res && res.code === 0 && res.data && res.data.records) {
          // 更新服务选择项
          const services = res.data.records.map(item => ({
            ...item,
            label: item.serviceName,
            value: item.id
          }));

          // 更新服务下拉框选项
          this.setFormData('serviceId', 'option', services, list);
          // 清空当前选择的服务
          this.setFormData('serviceId', 'value', null, list);
          this.setFormData('price', 'value', null, list);

          // 启用服务选择
          const serviceItem = list ? list.find(item => item.id === 'serviceId') : this.getFormData('serviceId');
          if(serviceItem) {
            this.$set(serviceItem, 'disabled', false);
          }
        }
      } catch (error) {
        console.error('根据分类筛选服务失败', error);
        this.$message.error('筛选服务失败');
      }
    },
  },
};
</script>
<style>
.orderListItem .content{
    height: 60vh;
    overflow-y: auto;
    padding: 0;
    min-height: 60vh;
  }
  .orderListItem .table-container{
    height: 30vh;
    overflow-y: auto;
    }
</style>
<style lang="scss" scoped>
 .orderListItem{
  height: 60vh;
  overflow: hidden;
}
.joinOrderShowMap{
  padding: 10px;
  background: rgb(224 255 238);
  margin-bottom: 10px;
  border-radius: 10px;
  .joinOrderShowMapItem{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    align-items: baseline;
    .orderItemTitle{
    width: 100px;
    margin-right: 10px;
  }
  }
}
.jointOrderHeader{
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.optionsOrder{
  transition: all 0.5s ease-in-out;
  max-height: 1000px; /* 设置为足够大的值以容纳内容 */
  opacity: 1;
  transform: translateY(0);
  overflow: hidden;
}
.packUp{
  max-height: 0 !important;
  opacity: 0;
  transform: translateY(-10px);
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}

.HeadPathBox{
  position: relative;
  .HeadPathBoxLoading{
    position: absolute;
    top: 2px;
    left: 26px;
    height: 25px;
    background: white;
    width: 162px;
    text-align: center;
    color: #888;
    font-size: 13px;
  }
}
.botPrompt{
  font-size: 12px;
  color: #606266;
  margin-left: 150px;
}
.form-box {
  display: flex;
  align-items: center;
  font-size: 14px;

  .form-label {
    width: 100px;
  }
}
.d-flex{
  display: flex;
  align-items: center;
}

.text {
  font-size: 14px;
}
.item {
  padding: 18px 0;
}
.box-card {
  width: 480px;
}

.form-title {
  color: #000;
  font-size: 16px;
  word-break: break-all;
  margin-bottom: 12px;
}

.line {
  padding-top: 24px;
  width: 100%;
  border-top: 1px solid #ccc;
  margin-top: 12px;
}

::v-deep .el-drawer__body {
  padding: 0 24px 24px;
}
.custom-table-spacing {
  margin-top: 16px; /* 上间距 */
  margin-bottom: 50px; /* 下间距 */
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-image {
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}
</style>
