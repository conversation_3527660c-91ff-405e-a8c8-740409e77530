/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
let prefix = '/manage/api'

//查询渠道商关联的所有经销商
export function listChannelAllOperator(data) {
  return requestV1.get(prefix + '/operator/listChannelAllOperator', data);
}

//获取经销商或渠道商所有未绑定通联
export function listNoBindMember(data) {
  return requestV1.get(prefix + '/operator/listNoBindMember', data);
}

//获取经销商或渠道商所有未绑定用户
export function listOperatorAllNoBindUser(data) {
  return requestV1.postJson(prefix + '/operator/listOperatorAllNoBindUser', data);
}

//查询经销商帐单表格分页数据
export function queryBalancePage(data) {
  return requestV1.postJson(prefix + '/operator/queryBalancePage', data);
}

//渠道商批量解绑设备
export function batchUnBindDevice(data) {
  return requestV1.postJson(prefix + '/operator/batchUnBindDevice', data);
}

//渠道商批量绑定设备
export function batchBindDevice(data) {
  return requestV1.postJson(prefix + '/operator/batchBindDevice', data);
}

//查询渠道商设备表格分页数据
export function queryChannelDevice(data) {
  return requestV1.postJson(prefix + '/operator/queryChannelDevice', data);
}

//添加渠道商
export function addChannel(data) {
  return requestV1.postJson(prefix + '/operator/addChannel', data);
}

//查询所有渠道商名称
export function listAllChannelToName(data) {
  return requestV1.get(prefix + '/operator/listAllChannelToName', data);
}


//查询经销商的所有下级经销商
export function listSubOperator(data) {
  return requestV1.get(prefix + '/operator/listSubOperator', data);
}

//编辑经销商信息
export function editOperator(data) {
  return requestV1.putJson(prefix + '/operator/editOperator', data);
}

//删除经销商
export function deleteOperator(data) {
  return requestV1.deleteForm(prefix + '/operator/deleteOperator', data);
}

//添加经销商
export function addOperator(data) {
  return requestV1.postJson(prefix + '/operator/addOperator', data);
}

//查询表格分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix + '/operator/queryPage', data);
}

//查询所有经销商名称
export function listAllOperatorToName(data) {
  return requestV1.get(prefix + '/operator/listAllOperatorToName', data);
}

//查询所有经销商下面的医院
export function listOperatorAllHospital(data) {

  return requestV1.postJson(prefix + '/hospital/listOperatorAllHospital', data);
}


//查询所有经销商
export function queryList(data) {
  return requestV1.postJson(prefix + '/operator/queryList', data);
}

//更新设备费是否优先扣除
export function updateDeviceSubsidy(data) {
  return requestV1.putJson(prefix + '/operator/updateDeviceSubsidy', data)
}

// 根据渠道商id获取渠道商绑定的设备
export function listOperatorDeviceByOperatorId(data) {
  return requestV1.get(prefix + '/operator/listOperatorDeviceByOperatorId', data)
}
