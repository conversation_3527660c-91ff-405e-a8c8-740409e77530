<template>
  <el-dialog
    :title="`批量克隆到租户`"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    center
    append-to-body
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '100px' }"
    >
      <div slot="btnList">
        <el-button
          type="primary"
          size="mini"
          @click="confirm"
          :loading="confirmLoading"
          >确定</el-button
        >
        <el-button type="danger" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
    <result-tab :show.sync="resultTabShow" :tableData="resultTabData" @close="close" />
  </el-dialog>
</template>

<script>
import formMixin from "@/mixin/formMixin";
import { cloneTaskToTargetTenant } from '@/api/todotasks.js'
import { tenantQueryList } from '@/api/v1/tenant'
import { getFromData } from '@/utils/index'
import resultTab from './result-tab.vue';
const defaultFormList = [
  {
    title: "租户",
    id: "targetTenantId",
    value: null,
    type: 2,
    option: [],
    must: true
  }
]

export default {
  mixins: [formMixin],
  components: {
    resultTab
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    paramsData: {
      type: Array,
      default: function () {
        return [];
      },
    }
  },
  data() {
    return {
      formList: JSON.parse(JSON.stringify(defaultFormList)),
      confirmLoading: false,
      dialogVisible: false,
      resultTabShow: false,
      resultTabData: []
    };
  },
  methods: {
    getTenantListAll() {
      tenantQueryList({}).then(res => {
        res.data.map(i => {
          i.label = i.tenantName
          i.value = i.id
        })
        this.setFormData('targetTenantId', 'option', res.data)
      })
    },
    async confirm() {
      let formParam = getFromData(this.formList);
      if (!formParam) return;
      let res = null;
      formParam = {
        ...formParam,
        taskIds: Array.isArray(this.paramsData) ? this.paramsData.map(item => item.id) : this.paramsData.id,
      };
      this.confirmLoading = true
      res = await cloneTaskToTargetTenant(formParam).catch(() => { this.confirmLoading = false })
      this.confirmLoading = false
      if (!this.$validate.isNull(res.data) && Array.isArray(res.data)) {
        this.resultTabData = res.data
        this.resultTabShow = true
        return
      }
      this.close('query')
      this.$eltool.successMsg(res.msg);
    },
    clear() {
      this.formList.forEach(item => {
        item.value = null;
      })
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit('update:show', false)
      this.$emit("close", type);
    },
  },
  watch: {
    show(n) {
      this.dialogVisible = n;
      if (!n) {
        this.formList = JSON.parse(JSON.stringify(defaultFormList))
        return
      }
      this.getTenantListAll()
    }
  },
};
</script>

<style lang="scss" scoped>
</style>