import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//批量启动暂停计划
export function startPauseFansPlan(data) {
     return requestV1.putJson(prefix+'/wxFansPlan/startPauseFansPlan', data);
}

//批量暂停计划
export function pauseFansPlan(data) {
    return requestV1.putJson(prefix+'/wxFansPlan/pauseFansPlan', data);
}

//批量更新投放计划设备权重
export function batchUpdatePlanDeviceWeight(data) {
    return requestV1.putJson(prefix+'/wxFansPlan/batchUpdatePlanDeviceWeight', data);
}

//批量清空设备投放计划列表
export function batchRemoveDeviceAllPlan(data) {
     return requestV1.putJson(prefix+'/wxFansPlan/batchRemoveDeviceAllPlan', data);
}

//批量添加设备到投放计划列表
export function batchAddDevicesToBatchPlan(data) {
      return requestV1.putJson(prefix+'/wxFansPlan/batchAddDevicesToBatchPlan', data);
}

//更新投放计划设备权重
export function updatePlanDeviceWeight(data) {
       return requestV1.putJson(prefix+'/wxFansPlan/updatePlanDeviceWeight', data);
}

//查询所有投放计划
export function queryList(data) {
     return requestV1.postJson(prefix+'/wxFansPlan/queryList', data);
}

//批量开启计划
export function startFansPlan(data) {
      return requestV1.putJson(prefix+'/wxFansPlan/startFansPlan', data);
}

//停止计划
export function stopFansPlan(data) {
   return requestV1.putJson(prefix+'/wxFansPlan/stopFansPlan', data);
}

//批量取消设备到投放计划
export function batchRemoveDeviceToPlan(data) {
     return requestV1.putJson(prefix+'/wxFansPlan/batchRemoveDeviceToPlan', data);
}

//批量添加设备到投放计划
export function batchAddDeviceToPlan(data) {
     return requestV1.postJson(prefix+'/wxFansPlan/batchAddDeviceToPlan', data);
}

//分页获取计划已经绑定的设备
export function listBindFansPlanDevice(data) {
     return requestV1.postJson(prefix+'/wxFansPlan/listBindFansPlanDevice', data);
}


//分页获取所有未绑定计划的设备
export function listNoBindFansPlanDevice(data) {
     return requestV1.postJson(prefix+'/wxFansPlan/listNoBindFansPlanDevice', data);
}

//编辑投放计划第一步
export function editFansPlan(data) {
   return requestV1.putJson(prefix+'/wxFansPlan/editFansPlan', data);
}

//添加投放计划第一步
export function addFansPlan(data) {
   return requestV1.postJson(prefix+'/wxFansPlan/addFansPlan', data);
}

//查询表格分页数据
export function queryPage(data) {
   return requestV1.postJson(prefix+'/wxFansPlan/queryPage', data);
}
