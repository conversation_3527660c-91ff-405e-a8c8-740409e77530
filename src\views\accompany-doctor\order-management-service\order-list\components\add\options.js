import {getAccompanyServicePage,accompanybookCloneOrder,getAccompanyemployeePage,getAccompanyproviderQueryAll,getAccompanyprovideruserQueryOne ,accompanybookUpdateOrder,createAccompanybook,accompanybookCancel,accompanybookDispatchProvider,accompanyemployeeChangeEmployee,accompanybookFinish,accompanybookTransfer,accompanycombineorderCreate,queryOfflinePage,accompanyserviceclassifyQueryPage,accompanybookFinishOrderRefund} from "@/api/dmCommunity/accompany-doctor.js";
import provinces from '@/utils/area.js'
import {getSource,getProvinceMap,getCityMap} from '@/views/accompany-doctor/getSource.js'

let provincesTwo = provinces.map(e=>({
    ...e,
    children:e.children.map(({label,value})=>({label,value}))
}))
export default async (type,providerId,currentProvider,paramsData)=>{
    // 获取查询条件
    let condition = {providerId,source:await getSource(providerId)}
    const servicePage = await getAccompanyServicePage({current:0,size:1000,condition})
    servicePage.data.records.map(e=>{
        e.label = e.serviceName
        e.value = e.id
    })

    // 获取服务分类列表
    let classifyPage;
    try {
      console.log('获取服务分类列表, providerId:', providerId);
      classifyPage = await accompanyserviceclassifyQueryPage({
        current: 1,
        size: 1000,
        condition: {
          state: 1, // 只获取启用状态的分类
          providerId: providerId
        }
      });
      console.log('服务分类数据:', classifyPage?.data?.records);
    } catch (error) {
      console.error('获取服务分类列表失败:', error);
    }

    const classifyOptions = classifyPage?.data?.records?.map(item => ({
      label: item.name,
      value: item.id,
      id: item.id
    })) || [];

    // 获取所有服务商下的城市信息
    let {provinceMap,AccompanyproviderQueryAll} = await getProvinceMap();
    // 获取当前服务商的城市信息
    let currentCityMap = getCityMap([currentProvider]);
    const options = {
        currentProvider,
        // 创建订单
        add:[
            { title: "就诊人", id: "bookName", value: null, type: 20},
            { title: "就诊人电话", id: "bookPhone", value: null, type: 1,option:[]},
            { title: "服务分类", id: "classifyId", value: null, type: 2, option: [], must: true },
            { title: "服务", id: "serviceId", value: null, type:2,option:servicePage.data.records,must: true},
            { title: "服务价格", id: "price", value: null, type: 1},
            { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false}},
            { title: "就诊医院", id: "hospitalName", value: null, type: 2,option:[]},
            { title: "医院科室", id: "deptName", value: null, type: 2,option:[]},
            { title: "就诊医生", id: "doctorName", value: null, type: 2,option:[]},
            { title: "陪诊时间", id: "startTimeMap", value: null, type: 6,dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss',option:[]},
            { title: "是否转单", id: "transferOrder", value: 0, type: 8, activeValue: 1, inactiveValue: 0},
            { title: "", id: "transferOrderTip", type: 24, tipContent: "备注：特殊原因无法服务，转回平台"},
            { title: '是否为平台单', type: 8, id: 'platformOrder', value: 0,activeValue: 1, inactiveValue: 0, inactiveColor: "#C0CCDA", },
            { title: "服务模式", id: "store", value: 1, type: 2, option: [{label:'云陪诊',value:1},{label:'独立小程序',value:0}], must: true,hidden:true },
            { title: "备注", id: "remark", value: null, type: 1,option:[]},
            { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: []},
        ],
        // 克隆订单
        cloneOrder: (() => {
            // 基础表单配置
            const baseFields = [
                { title: "就诊人", id: "bookName", value: null, type: 20},
                { title: "就诊人电话", id: "bookPhone", value: null, type: 1,option:[]},
                { title: "服务分类", id: "classifyId", value: null, type: 2, option: [], must: true },
                { title: "服务", id: "serviceId", value: null, type:2,option:servicePage.data.records,must: true},
                { title: "服务价格", id: "price", value: null, type: 1},
                { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false}},
                { title: "就诊医院", id: "hospitalName", value: null, type: 2,option:[]},
                { title: "医院科室", id: "deptName", value: null, type: 2,option:[]},
                { title: "就诊医生", id: "doctorName", value: null, type: 2,option:[]},
                { title: "陪诊时间", id: "startTimeMap", value: null, type: 6,dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss',option:[]},
                { title: "是否转单", id: "transferOrder", value: 0, type: 8, activeValue: 1, inactiveValue: 0},
                { title: "", id: "transferOrderTip", type: 24, tipContent: "备注：特殊原因无法服务，转回平台"}
            ];

            // 只在待接入(1)和待支付(2)状态下显示【是否为平台单】字段
            if (paramsData && (paramsData.orderState === 1 || paramsData.orderState === 2)) {
                baseFields.push({ title: '是否为平台单', type: 8, id: 'platformOrder', value: 0,activeValue: 1, inactiveValue: 0, inactiveColor: "#C0CCDA", });
            }

            // 添加其他字段
            baseFields.push(
                { title: "服务模式", id: "store", value: 1, type: 2, option: [{label:'云陪诊',value:1},{label:'独立小程序',value:0}], must: true,hidden:true },
                { title: "备注", id: "remark", value: null, type: 1,option:[]},
                { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: []}
            );

            return baseFields;
        })(),
        // 创建联合订单
        CreationJointOrder:[
            {
                title:'',
                id:'accompanyOrderDTOList',
                type:20,
                value: [
                    {
                        list:[
                            { title: "服务分类", id: "classifyId", value: null, type: 2, option: classifyOptions, must: true },
                            { title: "服务", id: "serviceId", value: null, type:2,option:servicePage.data.records,must: true},
                            { title: "服务价格", id: "price", value: null, type: 1,must: true},
                            { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false},must: true},
                            { title: "就诊医院", id: "hospitalName", value: null, type: 2,option:[],must: true},
                            { title: "医院科室", id: "deptName", value: null, type: 2,option:[]},
                            { title: "就诊医生", id: "doctorName", value: null, type: 2,option:[]},
                            { title: "陪诊时间", id: "startTimeMap", value: null, type: 6,dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss',option:[],must: true},
                            { title: "备注", id: "remark", value: null, type: 1,option:[]},
                            { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: []},
                        ],
                        isPackUp:false
                    }
                ]
            }

        ],
        // 查看
        check:[
            { title: "订单号", id: "id", value: null, type: 1,option:[] ,disable:true},
            { title: "就诊人", id: "bookName", value: null, type: 1,option:[] ,disable:true},
            { title: "就诊人电话", id: "bookPhone", value: null, type: 1,option:[] ,disable:true},
            { title: "服务项目", id: "serviceName", value: null, type: 1,option:[] ,disable:true},
            { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false},disable:true},
            { title: "医院科室", id: "deptName", value: null, type: 2,option:[],disable:true},
            { title: "就诊医生", id: "doctorName", value: null, type: 2,option:[],disable:true},
            { title: "就诊医院", id: "hospitalName", value: null, type: 2,option:[],disable:true},
            { title: "就诊时间", id: "startTime", value: null, type: 6,dateType:"date",format:'yyyy-MM-dd HH:mm:ss',option:[] ,disable:true},
            { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: [],disable:true},
            { title: "备注", id: "remark", value: null, type: 1,option:[],disable:true},

        ],
        // 创建服务单
        cheateOrder:[
            { title: "就诊城市",id: "provinces", value: [], type: 14,option:currentCityMap,props: {multiple: false}},
            { title: "服务分类", id: "classifyId", value: null, type: 2, option: classifyOptions, must: true },
            { title: "选择服务", id: "serviceId", value: null, type:2,option:servicePage.data.records,must: true},
            { title: "服务价格", id: "price", value: null, type: 1},
            { title: "服务模式", id: "store", value: 1, type: 2, option: [{label:'云陪诊',value:1},{label:'独立小程序',value:0}], must: true,hidden:true },
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
            { title: "就诊医院", id: "hospitalName", value: null, type:2,option:[],must: true},
            { title: "陪诊时间", id: "startTimeMap",dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss', value: null, type:6,option:[],must: true},
        ],
        // 取消订单
        cancelOrder:[
            { title: "取消原因", id: "cancelReason", value: null, type: 1,inputType:'textarea',must: true},
            { title: "退款金额", id: "refundAmount",  value: 0, type: 12,step:0.01,precision:2,min:0,must: true},
            { title:"",WarningTip:"取消原因用户端可见", id:'tag', type: 20},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // 订单二维码
        orderCode:[
          { title: "就诊人", id: "bookName", value: null, type: 1,option:[] ,disable:true},
          { title: "就诊人电话", id: "bookPhone", value: null, type: 1,option:[] ,disable:true},
          { id: "orderCode", title: "订单二维码",type:10, width: "120px",value: null,disable:true},
          { id: "posterImage", title: "订单海报",type:20, width: "120px",value: null,disable:true,hidden:true},
          { id: "showJoinOrder", title: "订单合计",type:20, width: "120px",value: null,hidden:true},
        ],
        // 更改订单
        changeOrder:[
            // { title: "服务", id: "serviceId", value: null, type:2,option:servicePage.data.records,must: true},
            { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false}},
            { title: "就诊医院", id: "hospitalName", value: null, type:2,option:[],must: true},
            { title: "陪诊时间", id: "startTimeMap",dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss', value: null, type:6,option:[],must: true},
            { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: []},
            { title: "备注", id: "remark", value: null, type: 1,option:[]},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // 更改预约
        ChangingReservation:[
            { title: "就诊城市",id: "provinces", value: [], type: 14,option:provinceMap ,props: {multiple: false},hidden: true},
            { title: "就诊医院", id: "hospitalName", value: null, type: 2,option:[]},
            { title: "医院科室", id: "deptName", value: null, type: 2,option:[],hidden: true},
            { title: "就诊医生", id: "doctorName", value: null, type: 2,option:[],hidden: true},
            { title: "陪诊时间", id: "startTimeMap",dateType:"datetimerange",format:'yyyy-MM-dd HH:mm:ss', value: null, type:6,option:[],must: true},
            { title: "补充内容", id: "backupImg", type: 10,option:[],multiple:true,limit:10,value: [],hidden: true},
            { title: "备注", id: "remark", value: null, type: 1,option:[]},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
            { title: "服务id", id: "serviceId", value: null, type:200,hidden: true},
        ],
        // 派单
        dispatcher:[
            { title: "派单模式", id: "mode", value: null, type:15,option:[{value:1,label:'指定派发'},{value:2,label:'抢单'}],must: true},
            { title: "陪诊师", id: "employeeId", value: null, type:2,option:[],must: true,hidden: true},
            { title: "平台备注", id: "remark", value: null, type: 1,inputType:'textarea'},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
            { title:"",WarningTip:"平台备注仅陪诊师可见", id:'tag', type: 20},
        ],
        // 转单
        transfer:[
            { title: "", id: "textContent", value: '是否转单？是否转单给小葫芦陪诊平台，由平台转给其它服务商进行服务', type: 1,inputType:'textarea',disable:true},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // 重新派单
        againDispatcher:[
            { title: "派单模式", id: "mode", value: null, type:15,option:[{value:1,label:'指定派发'},{value:2,label:'抢单'}],must: true},
            { title: "陪诊师", id: "employeeId", value: null, type:2,option:[],must: true,hidden: true},
            { title: "平台备注", id: "remark", value: null, type: 1,inputType:'textarea'},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
            { title:"",WarningTip:"平台备注仅陪诊师可见", id:'tag', type: 20 ,tagType:'warning'},

        ],
        // 更换陪诊师
        changeDesigners:[
            { title: "陪诊师", id: "employeeId", value: null, type:2,option:[],must: true},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // 服务记录
        serviceRecord:[

        ],
        // 诊断报告
        diagnosisReport:[

        ],
        // 结束服务
        closeService:[
            { title:"",WarningTip:"提示：当前操作将强制结束服务，陪诊师无法进行服务打卡，请谨慎操作", id:'tag', type: 20, tagType:'error'},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // 完成订单退款
        finishOrderRefund:[
            { title: "服务费", id: "payPrice", value: null, type: 1, disable: true, noSubmit: true},
            { title: "", id: "refundTip", type: 24, tipContent: "提示：将从【已收款金额】退款给用户，请确保【已收款金额】有足够金额"},
            { title: "退款原因", id: "refundReason", value: null, type: 1,inputType:'textarea',must: true},
            { title: "退款金额", id: "refundAmount",  value: 0, type: 12,step:0.01,precision:2,min:0,must: true},
            { title:"",WarningTip:"退款原因用户端可见", id:'tag', type: 20},
            { title: "订单id", id: "id", value: null, type:200,hidden: true},
        ],
        // lakala绑定订单
        bindLKLOrder:(()=>{
            let arr = [
                { prop: "logNo", label: "拉卡拉订单号", width: "80px" },
                { prop: "tradeStatus", label: "交易状态", width: "80px"},
                { prop: "totalAmount", label: "交易金额", width: "80px"},
                { prop: "tradeTime", label: "交易时间", width: "80px" },
                { prop: "remark", label: "线下收款码备注", width: "80px"},
            ]
            arr.searchFunc = queryOfflinePage;
            arr.getFromData = condition=>{
                condition.endTime && (condition.tradeEndTime = condition.endTime.replace(/[-:\s]/g, ''));
                delete condition.endTime;
                condition.startTime && (condition.tradeStartTime = condition.startTime.replace(/[-:\s]/g, ''));
                delete condition.startTime;
                delete condition.createTimeMap;
                condition.bindStatus = 1;
            };
            arr.selectFrom = [
                { title: "交易时间", id: 'createTimeMap',format:'yyyy-MM-dd-HH-mm', value: [], option: [], type: 6, defaultTime: ['00:00:00', '23:59:59'], dateType: 'datetimerange' },
                { title: "拉卡拉订单号", id: "logNo", value: null, type: 1,option:[], labelWidth: "100px",width:'200px',size:12 },
            ]
            let tradeStatusMap = {
                SUCCESS:'成功',
                REFUND:'退款',
                CLOSE:'关闭',
            }
            arr.setOptions = (data)=>{
                data.records.map(item=>{
                    item.tradeStatus = tradeStatusMap[item.tradeStatus];
                    item.totalAmount = item.totalAmount/100;
                    item.tradeTime = item.tradeTime.replace(
                        /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
                        '$1-$2-$3 $4:$5:$6'
                      );
                })
            }
            return arr;
        })(),
    }
    const buttonMap = {
        // 新增
        add:[{butName:'取消',type:'info',id:'clear'},{butName:'创建订单',type:'primary',id:'cheateOrder',apiFunc:createAccompanybook}],
        // 新增
        cloneOrder:[{butName:'取消',type:'info',id:'clear'},{butName:'克隆订单',type:'primary',id:'cheateOrder',apiFunc:accompanybookCloneOrder}],
        // 创建联合订单
        CreationJointOrder:[{butName:'上一步',type:'primary',id:'showPatientInformation'},{butName:'下一步',type:'primary',id:'cheateOrder',apiFunc:accompanycombineorderCreate}],
        // 查看
        check:[],
        // 创建服务单
        cheateOrder:[{butName:'取消',type:'info',id:'clear'},{butName:'发送给客户',type:'primary',id:'cheateOrder',apiFunc:accompanybookUpdateOrder}],
        // 取消订单
        cancelOrder:[{butName:'取消订单',type:'info',id:'cancelOrder',apiFunc:accompanybookCancel}],
        // 订单二维码
        orderCode:[],
        // 更改订单
        changeOrder:[{butName:'确认更改',type:'primary',id:'changeOrder',apiFunc:accompanybookUpdateOrder}],
        // 更改预约
        ChangingReservation:[{butName:'确认更改',type:'primary',id:'ChangingReservation',apiFunc:accompanybookUpdateOrder}],
        // 派单
        dispatcher:[{butName:'查看就诊人信息',type:'primary',id:'checkInfo'},{butName:'确认派单',type:'primary',id:'dispatcher',apiFunc:accompanybookDispatchProvider}],
        // 转单
        transfer:[{butName:'取消转单',type:'info',id:'clear'},{butName:'确认转单',type:'primary',id:'transfer',apiFunc:accompanybookTransfer}],
        // 重新派单
        againDispatcher:[{butName:'确认派单',type:'primary',id:'againDispatcher',apiFunc:accompanybookDispatchProvider}],
         // 更换陪诊师
         changeDesigners:[{butName:'确认更换',type:'primary',id:'changeDesigners',apiFunc:accompanyemployeeChangeEmployee}],
         // 服务记录
         serviceRecord:[],
         // 诊断报告
         diagnosisReport:[],
         // 结束服务
        closeService:[{butName:'确认强制结束服务',type:'primary',id:'closeService',apiFunc:accompanybookFinish}],
        // 完成订单退款
        finishOrderRefund:[{butName:'确认退款',type:'primary',id:'finishOrderRefund',apiFunc:accompanybookFinishOrderRefund}],
        // 绑定LKL订单
        bindLKLOrder:[{butName:'取消',type:'info',id:'clear'},{butName:'确定',type:'primary',id:'bindLKLOrder',apiFunc:accompanybookFinish}],
    }
    const showTitleMap = {
        add:'创建订单',
        cloneOrder:'克隆订单',
        CreationJointOrder:'创建联合订单',
        check:'查看',
        cheateOrder:'创建服务单',
        cancelOrder:'取消订单',
        orderCode:'订单二维码',
        changeOrder:'更改订单',
        ChangingReservation:'更改预约',
        dispatcher:'派单',
        transfer:'转单',
        againDispatcher:'重新派单',
        changeDesigners:'更换陪诊师',
        serviceRecord:'服务记录',
        diagnosisReport:'诊断报告',
        closeService:'强制结束服务',
        finishOrderRefund:'订单退款',
        bindLKLOrder:'绑定拉卡拉订单',
    }
    const tabList = ['bindLKLOrder'];
    const isTabList = tabList.includes(type);
    return {options:options[type],buttonMap:buttonMap[type],showTitleMap,isTabList}
}
