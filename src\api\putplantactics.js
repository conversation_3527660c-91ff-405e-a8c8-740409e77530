import requestV1 from '@/common/utils/modules/request'

let prefix = '/adservice/api/v1'

//更新策略状态
export function updateExtendStatus(data) {
    return requestV1.putJson(prefix + '/putplantactics/updateExtendStatus', data);
}
//根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/putplantactics/query/list', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/putplantactics/query/page', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/putplantactics/update', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/putplantactics/query/one', data);
}

//根据投放创意id获取广告位
export function getAdsPosition(data) {
    return requestV1.get(prefix + '/putplantactics/getAdsPosition', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/putplantactics/insert', data);
}

//查询绑定未绑定设备
export function queryDevicePage(data) {
    return requestV1.postJson(prefix + '/putplantactics/queryDevicePage', data);
}

//批量绑定设备
export function batchBindDevice(data) {
    return requestV1.putJson(prefix + '/putplantactics/batchBindDevice', data);
}

//批量解绑设备
export function batchUnboundDevice(data) {
    return requestV1.putJson(prefix + '/putplantactics/batchUnboundDevice', data);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/putplantactics/delete/batch/${data}`);
}