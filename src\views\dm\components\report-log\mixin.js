// 服务器端导出
import {
  insert,
} from "@/service/api/modules/dm/exporttasklog";
export default {
  props: {
    selectIds: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // 是否横向 true 是
    landscape: {
      type: Boolean,
      default: false,
    },
    pagetype: {
      type: [String, Number],
      default: 1, // 1 结算报告 2 推广任务
    },
    noBusinessId: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      reportDefaultParams: {}, // 额外参数
      aHref: ''
    }
  },
  methods: {
    async previewDocx() {
      let row = this.selectIds[0]
      // 预览报告
      let location = window.location;
      let params = {
        // taskId: row.taskId,
        pagetype: this.pagetype,
        businessId: row.businessId,
        taskId: row.taskId ? row.taskId : null,
        additional: row.additional ? row.additional : null,
        ...this.reportDefaultParams
      };
      let str = "";
      for (let key in params) {
        if (str === "" && params[key]) {
          str += key + "=" + params[key];
        } else if (params[key]) {
          str += "&" + key + "=" + params[key];
        }
      }
      this.aHref =
        location.origin +
        location.pathname +
        "#" +
        `/servePrint?${str}`;

      this.$nextTick(() => {
        this.$refs.aRef.click();
      });
    },
    // landscape 是 true 横
    async init() {
      let landscape = this.landscape;
      if([3,4].includes(this.pagetype) && this.reportDefaultParams instanceof Object && this.reportDefaultParams.reportStyle === 2) {
        landscape = false;
      }
      this.dialogLoading = true;
      const list = [];
      console.log('this.selectIds[i]', this.selectIds)
      for (let i = 0; i < this.selectIds.length; i++) {
        let businessId = this.selectIds[i].businessId;
        let businessLevelId = this.selectIds[i].businessLevelId
        if (this.pagetype === 15) {
          // 陪诊
          businessId = this.getBusinessId()
        }
        // to
        const obj = await this.exportRequest({
          businessId, // 业务ID
          taskId: this.selectIds[i].taskId,
          businessType: this.pagetype,
          additional: this.selectIds[i].additional,
          businessLevelId,
        });
        // 是否横向
        obj.landscape = landscape;
        list.push(obj);
      }
      const res = await insert(list).catch(e => {
        this.dialogLoading = false;
      });

      this.dialogLoading = false;
      this.search();
    },
    // 导出
    async exportRequest({
      businessId,
      businessType,
      taskId,
      additional,
      pagetype,
      businessLevelId,
    }) {
      let businessType2;
      if (this.pagetype === 15) {
        businessType2 = businessId;
        businessId = this.selectIds[0].businessId;
      } else if (
        typeof businessId === "string" &&
        businessId.indexOf("-") !== -1
      ) {
        businessType2 = businessId.split("-").join("") - 0;
      } else {
        businessType2 = businessId;
      }
      let params = {
        pagetype: pagetype ? pagetype : this.pagetype,
        // satoken:getToken(),
        businessId: businessId,
        taskId: taskId,
        additional,
        ...this.reportDefaultParams
      };

      let str = "";
      for (let key in params) {
        if (str === "") {
          str += key + "=" + params[key];
        } else {
          str += "&" + key + "=" + params[key];
        }
      }
      let exportUrl =
        location.origin + location.pathname + "#" + `/servePrint?${str}`;
      let cparams = {};
      if ([12, 14, 16, 17, 20].includes(Number(businessType))) {
        cparams.businessLevelId = businessLevelId;
      }
      const paramsDefault = {
        businessId: businessType2, // 塞任务id
        businessType: businessType, // 业务类型:1-项目报告，2-个人结算报告		false
        exportUrl, // 路径
        ...cparams,
      };

      return paramsDefault;
    },
    search() {

    }
  }
}