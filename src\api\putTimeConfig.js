/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

export function listAllUserMsgPushConfig(data) {
  return requestV1.get(prefix+'/userMsgPushConfig/listAllUserMsgPushConfig', data);
}

export function addOrUpdate(data) {
  return requestV1.postJson(prefix+'/userMsgPushConfig/addOrUpdate', data);
}

export function deletes(data) {
  return requestV1.deleteJson(prefix+'/userMsgPushConfig/deletes', data);
}

export function findById(data) {
  return requestV1.get(prefix+'/userMsgPushConfig/findById', data);
}
