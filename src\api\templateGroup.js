import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//删除模板分组
export function deletes(data) {
     return requestV1.deleteForm(prefix+'/templateGroup/deletes', data);
}

//根据模板消息id获取分组
export function getGroupListBytemplateId(data) {
    return requestV1.get(prefix+'/templateGroup/getGroupListBytemplateId', data);
}

//编辑模板分组
export function edit(data) {
     return requestV1.putJson(prefix+'/templateGroup/edit', data);
}

//新增模板分组
export function add(data) {
  return requestV1.postJson(prefix+'/templateGroup/add', data);
}

//当新建分组时根据模板消息ID获取默认配置列表
export function getDefaultConfigList(data) {
     return requestV1.get(prefix+'/templateGroup/getDefaultConfigList', data);
}


//根据ID获取模板分组详情
export function findById(data) {
      return requestV1.get(prefix+'/templateGroup/findById', data);
}
