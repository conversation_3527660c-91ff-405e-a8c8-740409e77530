/* eslint-disable */
import env from '@/config/env'
/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
    //短链excel导入

// excel导入数据
const uploadExcel = env.ctx + prefix + '/shortUrl/uploadExcel'
export { uploadExcel }


//新增
export function add(data) {
    return requestV1.postJson(prefix + '/shortUrl/add', data);
}

//删除
export function deletes(data) {
    return requestV1.deleteForm(prefix + '/shortUrl/delete', data);
}

//编辑
export function edit(data) {
    return requestV1.putJson(prefix + '/shortUrl/edit', data);
}

//导出渠道数据
export function exportCode(data) {
    return requestV1.getBlob(prefix + '/shortUrl/exportCode', data);
}

//查询详细的点击列表
export function queryClickPage(data) {
    return requestV1.postJson(prefix + '/shortUrl/queryClickPage', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/shortUrl/queryPage', data);
}