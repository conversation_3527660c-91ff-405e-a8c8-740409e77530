<template>
  <div class="topic-rect-page-v2-v6">
    <template v-for="(page, pageIndex) in pageContent">
      <div
        class="topic-rect-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="topic-rect-top"></div>
        <div
          class="topic-statistics pull-table-box table-size_noscale"
          v-if="pageIndex === 0 && inType === 'topic-statistics'"
        >
          <div class="pull-table">
            <el-table
              :data="statistTable"
              header-row-class-name="pull-new-details-table-row"
              cell-class-name="pull-new-details-table-cell"
              style="width: 100%"
            >
              <template v-for="col in statistHeader">
                <el-table-column
                  :prop="col.prop"
                  :label="col.label"
                  :width="col.width"
                  align="center"
                  :key="col.prop"
                  :isReportTable="true"
                >
                </el-table-column>
              </template>
            </el-table>
          </div>
        </div>
        <div class="topic-rect-ct" v-if="page.once">
          <div class="topic-rect-ctl"></div>
          {{ page.pageTitle }}{{ page.nextStr }}
        </div>
        <div class="flex-box">
          <div class="topic-echart">
            <div class="echart-item" :id="page.echatUuid"></div>
          </div>
          <div class="pull-table-box table-size18" :id="page.uuid">
            <div
              class="pull-table"
              :style="{
                width: tableWidth + 'px',
                zoom: widthZoom,
                '--borderWidth': borderWidth,
              }"
            >
              <el-table
                :data="page.children"
                header-row-class-name="pull-new-details-table-row"
                cell-class-name="pull-new-details-table-cell"
                style="width: 100%"
              >
                <template v-for="col in tableHeader">
                  <el-table-column
                    :prop="col.prop"
                    :label="col.label"
                    :width="col.width"
                    align="center"
                    :key="col.prop"
                    :isReportTable="true"
                  >
                  </el-table-column>
                </template>
              </el-table>
            </div>
            <div class="botton-text">实际提交：{{ page.submitCount }}份</div>
          </div>
        </div>
        <div class="bottom-border">
          <div class="bottom-tb"></div>
          <div class="bottom-bb"></div>
          <div class="bottom-lb"></div>
          <div class="bottom-rb"></div>
          <template v-if="pagetype === 3">
            本次“{{ page.pageTitle }}”的反馈样本中，</template
          >
          <template v-else>
            本报告样本，数据收集数量 {{ page.submitCount }} 份， </template
          >{{ page.bottomStr }}
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";

export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "topic-rect-page-v2-v6",
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      pageHeight: this.pageSize.height - 53,
      boxWidth: this.pageSize.width - 400,
      tableWidth: this.pageSize.width - 400,
      borderWidth: null,
      widthZoom: 1,
      subTitle: "",
      pageContent: [],
      statistHeader: [
        {
          label: "当前报告总题目",
          prop: "totalNum",
          width: 235,
        },
        {
          label: "总参与人数",
          prop: "totalPersonNum",
          width: 235,
        },
        {
          label: "分析维度",
          prop: "label",
          width: 235,
        },
      ],
      statistTable: [
        {
          totalNum: 0,
          totalPersonNum: 0,
          label: "主体分析",
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
    renderUpdateCount(n) {
      this.initRenderChart();
    },
  },
  methods: {
    initRenderChart() {
      let pageObject = this.pageObject || {};
      for (let i = 0; i < this.pageContent.length; i++) {
        let item = this.pageContent[i];
        if (!item.once) {
          continue;
        }
        let chartUuidOne = "#" + item.echatUuid;
        let answerOptionVoList = item.children.map((citem) => {
          return {
            value: citem.selectOptionNum,
            // name: citem.optionValue,
            name: this.getTargetText(citem.optionValue, 20),
            selectOptionProportion: citem.selectOptionProportion,
          };
        });
        let chartOne = {
          belongType: 2,
          seriesData: answerOptionVoList,
        };
        if (this.inType === "customer-feedback-page1") {
          this.initBarChart2DV2(chartOne, chartUuidOne, {
            barWidth: 11,
            xAxisOption: {
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            },
            yAxisOption: {},
            seriesOption: {
              showBackground: true,
              backgroundStyle: {
                color: "#D0DDE9",
              },
            },
            labelOption: {
              show: false,
            },
            color: "#70A5D7",
            gridOption: {
              left: 64,
              right: 10,
              top: 0,
            },
          });
        } else if (
          [
            "keyword-description-page",
            "customer-feedback-page",
            "topic-statistics",
          ].includes(this.inType)
        ) {
          let userColorResult = [
            "#FDDF7B",
            "#E6A380",
            "#F4908F",
            "#C7AAF8",
            "#609BD3",
          ];
          if (answerOptionVoList.length === 3) {
            userColorResult = ["#F4908F", "#609BD3", "#FDDF7B"];
          }
          this.initRadioChart2D(chartOne, chartUuidOne, {
            openUserColor: true,
            userColorResult: userColorResult,
            labelOption: {
              show: false,
            },
            seriesOption: {
              center: ["50%", 120],
              radius: "80%",
            },
            legendOption: {
              left: 0,
              top: 260,
              icon: "circle",
              itemWidth: 19,
              itemHeight: 19,
              orient: "vertical",
              textStyle: {
                fontSize: 16, // 字体大小
                lineHeight: 25, // 行高（关键配置）
                color: "#333", // 字体颜色
                fontWeight: "bold",
              },
            },
          });
        } else {
          this.initProbleDataChart(chartOne, 0, chartUuidOne);
        }
      }
    },
    async initRender() {
      let pageObject = this.pageObject || {};
      let list = pageObject.answerReportVoList || [];
      let totalNum = list.length;
      let totalPersonNum = 0;
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        let formTemplateOptions = item.formTemplateOptions || [];
        let bottomStr = "";
        totalPersonNum = item.allWriteOptionNum;
        if (this.pagetype === 3) {
          bottomStr =
            formTemplateOptions
              .map((item) => {
                return `选择“${item.optionValue}”的店员占${item.selectOptionProportion}`;
              })
              .join("，") + "。";
        } else {
          bottomStr =
            formTemplateOptions
              .map((item) => {
                return (
                  item.optionValue +
                  item.selectOptionNum +
                  "人，占比" +
                  item.selectOptionProportion
                );
              })
              .join("，") + "。";
        }
        for (let j = 0; j < formTemplateOptions.length; j++) {
          await this.rendCurrent(formTemplateOptions[j], {
            type: "topic-rect-page",
            once: true,
            echatUuid: "chart_v2_" + this.getUuid(),
            once: j === 0,
            children: [],
            pageTitle: item.title,
            nextStr:
              item.formType == 1
                ? "（单选）"
                : item.formType == 2
                ? "（多选）"
                : "",
            submitCount: list[i].submitCount,
            bottomStr: bottomStr,
          });
        }
        if (
          this.pageContent[this.waitIndex] &&
          formTemplateOptions.length > 0
        ) {
          this.waitIndex += 1;
        } else if (formTemplateOptions.length === 0) {
          this.pageContent.push({
            type: "topic-rect-page",
            once: true,
            echatUuid: "chart_v2_" + this.getUuid(),
            once: true,
            children: [],
            pageTitle: item.title,
            submitCount: list[i].submitCount,
          });
        }
      }
      if (this.inType === "topic-statistics") {
        this.statistTable[0].totalNum = totalNum;
        this.statistTable[0].totalPersonNum = totalPersonNum;
      }
      await this.$nextTick();
      this.trimSuccess();
    },
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    async initTableHeader() {
      let sw = this.boxWidth;
      let widthZoom = 1;
      let headersWidth = 0;
      this.tableHeader.forEach((item) => {
        headersWidth += item.width;
      });
      if (headersWidth < sw) {
        headersWidth = sw;
        widthZoom = 1;
        this.tableHeader[this.tableHeader.length - 1].width = null;
      } else {
        headersWidth += this.tableHeader.length * 2;
        this.tableHeader[this.tableHeader.length - 1].width = null;
        widthZoom = (sw / headersWidth).toFixed(3);
      }
      this.widthZoom = widthZoom;
      this.tableWidth = headersWidth;
      this.borderWidth =
        Math.ceil(this.calculateZoomFactor(widthZoom) * 1) + "px";
      await this.$nextTick();
      this.initRender();
    },
    initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      this.pageHeight = this.pageSize.height - 63;
      this.uuidKey = this.inType;
      this.initTableHeader();
    },
  },
  computed: {
    tableHeader() {
      let n = [];
      if (this.inType === "customer-feedback-page") {
        n = [
          {
            width: 250,
            prop: "optionValue",
            label: "回馈描述",
          },
          {
            width: 120,
            prop: "selectOptionNum",
            label: "人数",
          },
          {
            width: 120,
            prop: "selectOptionProportion",
            label: "占比",
          },
        ];
      } else if (this.inType === "keyword-description-page") {
        n = [
          {
            width: 250,
            prop: "optionValue",
            label: "关键字反馈",
          },
          {
            width: 120,
            prop: "selectOptionNum",
            label: "人数",
          },
          {
            width: 120,
            prop: "selectOptionProportion",
            label: "占比",
          },
        ];
      } else if (this.inType === "topic-statistics") {
        n = [
          {
            width: 220,
            prop: "optionValue",
            label: "选项",
          },
          {
            width: 150,
            prop: "selectOptionNum",
            label: "答题人数（人）",
          },
          {
            width: 120,
            prop: "selectOptionProportion",
            label: "占比",
          },
        ];
      }
      return n;
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
$borderColor: #70a5d7;
.topic-rect-page-v2-v6 {
  .topic-rect-page {
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    padding: 165px 45px 20px;
  }
  .topic-rect-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .topic-rect-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    // margin-bottom: 65px;
  }
  .topic-rect-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .echart-item {
    width: 100%;
    height: 100%;
  }
  .flex-box {
    display: flex;
  }
  .topic-echart {
    width: 277px;
    height: 500px;
    margin-right: 73px;
  }
  .bottom-border {
    // border: 1px dashed $borderColor;
    padding: 27px 23px;
    line-height: 1.5;
    color: #333333;
    font-size: 19px;
    color: #333333;
  }
  .botton-text {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    font-weight: 550;
    font-size: 16px;
    color: #333333;
  }
  .topic-statistics {
    margin-bottom: 25px;
  }
}
</style>