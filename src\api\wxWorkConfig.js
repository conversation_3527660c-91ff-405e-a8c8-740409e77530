/* eslint-disable */

import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api'

// 获取同步记录详情分页
export function queryDetailPage(data) {
    return requestV1.postJson(prefix + '/wxWorkConfig/queryDetailPage', data);

    // return request({
    //     url: '/api/wxWorkConfig/queryDetailPage',
    //     method: 'POST',
    //     data,
    //     baseURL
    // })
}

// 从企业微信同步标签
export function synCpTagToSaasAcount(data) {
    return requestV1.get(prefix + '/wxWorkConfig/synCpTagToSaasAcount', data);

}

// 查询已配置客户联系功能的企业微信用户列表
export function findListByAuthId(data) {
    return requestV1.get(prefix + '/wxWorkConfig/findListByAuthId', data);
}

// 编辑企业微信同步信息配置
export function edit(data) {
    return requestV1.postJson(prefix + '/wxWorkConfig/edit', data);

}

// 获取企业微信同步信息配置
export function getWxWorkConfig(data) {
    return requestV1.get(prefix + '/wxWorkConfig/getWxWorkConfig', data);
}

// 获取同步记录分页
export function queryPage(data) {
    return requestV1.postJson(prefix + '/wxWorkConfig/queryPage', data);

}
