import { formFenToYuan } from '@/utils/index'
export default {
  props: {
    taskId: {
      type: [Number, String],
      default: null,
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      pageSize: {
        width: 793,
        // height: process.env.VUE_APP_PREFIX_URL === '' ? 1122 : 1123,
        height: 1122
      },
      pageContent: [],
      pageLoading: false,
      initsuccesscount: 0,
      targetCount: 1,
      authHeightResult: {},
      totalPagingObject: {},
      pageNumObject: {},
      moduleRangeObject: {},
    }
  },
  methods: {
    initCountPage() {
      let pageNumObject = {};
      let moduleRangeObject = {};
      let pageIdx = 0;
      // console.log('======', this.totalPagingObject)
      this.pageContent.forEach(item => {
        let pageObject = this.totalPagingObject[item.type] || {};
        for (let key in pageObject) {
          pageNumObject[key] = pageObject[key] + pageIdx;
          // console.log('89999999999', pageNumObject[key], pageNumObject, pageIdx, key)
        }
        if (item.moduleKey) {
          let min = 0;
          let max = 0;
          for (let key in pageObject) {
            let num = pageNumObject[key];
            if (min === 0) {
              min = num
              max = num
            }
            if (min > num) {
              min = num;
            }
            if (max < num) {
              max = num;
            }
          }
          console.log('min===', min, max,item.type)
          if(!moduleRangeObject[item.moduleKey]) {
            if (min === max) {
              moduleRangeObject[item.moduleKey] = min;
            } else {
              if(max !== 0) {
                moduleRangeObject[item.moduleKey] = min + '-' + max;
              }
            }
          } else{
            let str = moduleRangeObject[item.moduleKey] + '';
            if(str.includes('-')) {
              let tarr = moduleRangeObject[item.moduleKey].split('-');
              if(max !== 0) {
                moduleRangeObject[item.moduleKey] = tarr[0] + '-' + max;
              }
            } else {
              if(max !== 0) {
                moduleRangeObject[item.moduleKey] = moduleRangeObject[item.moduleKey] + '-' + max;
              }
            }
          }
        }
        let len = Object.keys(pageObject).length;
        // console.log('pageObject===', item.type, pageObject, len)
        pageIdx += len;
      })
      pageNumObject.totalNum = pageIdx;
      this.pageNumObject = pageNumObject;
      this.moduleRangeObject = moduleRangeObject;
      console.log('this.pageNumObject======', this.pageNumObject, this.moduleRangeObject)
    },
    updatePageing(params = {}) {
      let { pageingObject, parentUuid } = params || {};
      if (parentUuid) {
        this.totalPagingObject[parentUuid] = pageingObject || {}
      }
    },
    getPrice(price) {
      price = formFenToYuan((price - 0) / 100)
      return price;
    },
    addComputeTag() {
      const dom = document.createElement("div");
      dom.classList = "pdf_finish";
      document.body.append(dom);
    },
    async updateSuccess() {
      this.initsuccesscount += 1;
      console.log('initsuccesscount===', this.initsuccesscount, this.targetCount)
      if (this.initsuccesscount === this.targetCount) {
        this.pageLoading = false;
        await this.$nextTick();
        let aDom = document.querySelector("a");
        if (aDom && Array.isArray(aDom)) {
          aDom[0] && aDom[0].remove();
        }
        await this.initCountPage();
        await this.pageInitMethod();
        setTimeout(() => {
          this.$emit("compute", {});
          this.addComputeTag();
        }, 300);
      }
    },
    async pageInitMethod() {

    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
  }
}