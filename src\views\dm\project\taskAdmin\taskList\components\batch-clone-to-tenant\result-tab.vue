<template>
  <el-dialog
    title="克隆结果"
    :visible.sync="visible"
    width="80%"
    :before-close="close"
    append-to-body
  >
    <div class="exportTable">
      <el-table :data="tableData" style="width: 100%">
        <template v-for="(item, index) in headers">
          <el-table-column :key="index" :label="item.title" :prop="item.prop">
            <template slot-scope="scope">
              <div v-if="item.prop === 'status'">
                <status-desc v-if="scope.row.status === 1" type="success" desc="成功" />
                <status-desc v-else type="danger" desc="失败" />
              </div>
              <div v-else>
                {{ scope.row[item.prop] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">返回列表</el-button>
    </span>
  </el-dialog>
</template>

<script>
import StatusDesc from '@/components/basics/status-desc'
export default {
  components: {
    StatusDesc
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      visible: false,
      tabData: [],
      headers: [
        { prop: "taskId", title: "任务编码", width: "180px" },
        { prop: "reason", title: "失败原因", width: "180px" },
        { prop: "status", title: "结果", width: "180px" },
      ]
    }
  },
  watch: {
    show(n) {
      this.visible = n
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false)
      this.$emit('close')
    }
  }
}
</script>

<style>

</style>