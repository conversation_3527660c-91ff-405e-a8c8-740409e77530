<template>
  <div class="topic-rect-page-v3-v6">
    <template v-for="page in pageContent">
      <div
        class="topic-rect-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="topic-rect-top"></div>
        <template v-if="page.once">
          <div class="topic-rect-ct">
            <div class="topic-rect-ctl"></div>
            {{ page.pageTitle }}
          </div>
          <div class="topic-echart">
            <div class="echart-item" :id="page.echatUuid"></div>
          </div>
        </template>
        <div class="pull-table-box table-size_noscale" :id="page.uuid">
          <div
            class="pull-table"
            :style="{
              width: tableWidth + 'px',
              zoom: widthZoom,
              '--borderWidth': borderWidth,
            }"
            v-if="page.children.length > 0"
          >
            <el-table
              :data="page.children"
              header-row-class-name="pull-new-details-table-row"
              cell-class-name="pull-new-details-table-cell"
              style="width: 100%"
            >
              <template v-for="col in tableHeader">
                <el-table-column
                  :prop="col.prop"
                  :label="col.label"
                  :width="col.width"
                  align="center"
                  :key="col.prop"
                  :isReportTable="true"
                >
                </el-table-column>
              </template>
            </el-table>
          </div>
          <div class="bottom-border" v-if="page.showBottom">
            根据统计信息，药店分布共计 {{ page.submitCount }} 个门店，{{
              page.bottomStr
            }}
          </div>
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";
export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "topic-rect-page-v3-v6",
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      pageHeight: this.pageSize.height - 53,
      boxWidth: 448,
      tableWidth: 448,
      borderWidth: null,
      widthZoom: 1,
      subTitle: "",
      pageContent: [],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
    renderUpdateCount(n) {
      this.initRenderChart();
    },
  },
  methods: {
    async initRender(list = []) {
      console.log("this.pageHeight", this.pageHeight);
      let bottomStr =
        list
          .map((item) => {
            return (
              item.optionValue +
              item.selectOptionNum +
              "家，占比" +
              item.selectOptionProportion
            );
          })
          .join("，") + "。";
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        await this.rendCurrent(item, {
          type: "pull-new-details-page",
          once: this.pageContent.length === 0,
          children: [],
          showBottom: false,
          bottomStr: bottomStr,
          pageTitle: '药店分布'
        });
      }
      if (this.pageContent.length > 0) {
        let endIdx = this.pageContent.length - 1;
        this.pageContent[endIdx].showBottom = true;
        await this.$nextTick();
        let endItem = this.pageContent[endIdx];
        let uuid = endItem.uuid;
        let { offsetHeight, offsetTop } = this.getRenderHeight(uuid);
        // let height =
        if (offsetHeight + offsetTop + this.errorGapCount > this.pageHeight) {
          this.pageContent[endIdx].showBottom = false;
          this.pageContent.push({
            type: "pull-new-details-page",
            once: this.pageContent.length === 0,
            children: [],
            uuid: this.getUuid(),
            showBottom: true,
          });
        }
      }
      this.trimSuccess({});
    },
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    initMethod() {
      let pageObject = this.pageObject || {};
      let planObjectListList = pageObject.planObjectListList || [];
      console.log("planObjectListList===", planObjectListList);
      this.initRender(planObjectListList);
    },
    initRenderChart() {
      for (let i = 0; i < this.pageContent.length; i++) {
        let item = this.pageContent[i];
        if (!item.once) {
          continue;
        }
        let chartUuidOne = "#" + item.echatUuid;
        let answerOptionVoList = item.children.map((citem) => {
          return {
            value: citem.selectOptionNum,
            name: citem.optionValue,
            selectOptionProportion: citem.selectOptionProportion + "%",
          };
        });
        let chartOne = {
          belongType: 2,
          seriesData: answerOptionVoList,
        };
        console.log("chartOne===", chartOne);
        if (answerOptionVoList.length > 0) {
          let xAxisOption = {};
          let yAxisOption = {};
          xAxisOption = {};
          yAxisOption = {
            axisLine: {
              show: false,
              lineStyle: {
                color: "#6e7079", // 轴线颜色
                width: 1, // 轴线宽度
                type: "solid", // 实线（默认值，可省略）
              },
            },
          };
          this.initBarChart2D(chartOne, chartUuidOne, {
            color: "#E6A380",
            xAxisOption: xAxisOption,
            yAxisOption: yAxisOption,
            userOption: {
              title: {
                // text: "数量：" + 150 + "家",
                left: "42%",
              },
            },
            labelOption: {
              formatter: function (params) {
                return params.value + "家";
              },
            },
          });
        }
      }
    },
  },
  computed: {
    tableHeader(n) {
      n = [];
      if (this.inType === "distribution-of-pharmacies") {
        n = [
          {
            label: "地区",
            prop: "optionValue",
            width: 149,
          },
          {
            label: "数量（家）",
            prop: "selectOptionNum",
            width: 149,
          },
          {
            label: "占比",
            prop: "selectOptionProportion",
            width: 149,
          },
        ];
      }
      return n;
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
$borderColor: #70a5d7;
.topic-rect-page-v3-v6 {
  .topic-rect-page {
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    padding: 165px 40px 20px;
  }
  .topic-rect-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .topic-rect-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-bottom: 33px;
  }
  .topic-rect-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .topic-echart {
    width: 100%;
    height: 328px;
  }
  .pull-table-box {
    padding-top: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .bottom-border {
    border: 1px dashed $borderColor;
    padding: 23px 21px;
    line-height: 1.5;
    color: #333333;
    margin-top: 76px;

    // font-weight: 500;
    font-size: 19px;
    color: #333333;
  }
  .echart-item {
    width: 100%;
    height: 100%;
  }
}
</style>