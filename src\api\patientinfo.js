/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * h患者档案
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/patientinfo/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/patientinfo/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/patientinfo/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/patientinfo/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/patientinfo/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/patientinfo/update`, data)
}

// 档案导入
export const upload = `${env.ctx + prefix}/patientinfo/upload`

