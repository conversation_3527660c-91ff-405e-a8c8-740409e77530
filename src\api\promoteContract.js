/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

//认证撤回
export function updateWithdrawStatus(data) {
    return requestV1.postForm(prefix + '/promoteContract/updateWithdrawStatus', data);

    // return request({
    //       url: '/api/promoteContract/updateWithdrawStatus',
    //       method: 'POST',
    //       data: qs.stringify(data),
    //       baseURL
    //   })
}

let prefix = '/sop/api'
    //发起认证(发送认证消息)
export function sendVerifyMsg(data) {
    return requestV1.postForm(prefix + '/promoteContract/sendVerifyMsg', data);
}

//查询list数据
export function queryList(data) {
    return requestV1.postJson(prefix + '/promoteContract/queryList', data);
}
//法务认证
export function legalVerify(data) {
    return requestV1.postJson(prefix + '/promoteContract/legalVerify', data);
}

//注销推广合同信息
export function deletes(data) {
    return requestV1.deleteJson(prefix + '/promoteContract/deletes', data);
}

//编辑推广合同信息
export function edit(data) {
    return requestV1.putJson(prefix + '/promoteContract/edit', data);
}

//新增推广合同信息
export function add(data) {
    return requestV1.postJson(prefix + '/promoteContract/add', data);
}

//根据ID获取推广合同信息信息
export function findById(data) {
    return requestV1.get(prefix + '/promoteContract/findById', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/promoteContract/queryPage', data);
}