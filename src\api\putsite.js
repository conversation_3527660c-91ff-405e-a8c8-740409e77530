/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/v1'

//查询合作商所属站点列表
export function querylistbybusinessInfoId(data) {
    return requestV1.get(prefix + '/putsite/query/list/by/businessInfoId', data);
}

//查询修改单价记录
export function queryListBySiteId(data) {
    return requestV1.get(prefix + '/putsite/queryListBySiteId', data);
}

//查询所有站点
export function queryList(data) {
    return requestV1.get(prefix + '/putsite/query/list', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/putsite/query/page', data);
}

//根据主键单一查询
export function queryoOne(data) {
    return requestV1.get(prefix + '/putsite/query/one', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/putsite/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/putsite/update', data);
}

//根据主键id指定删除
export function deleteOne(data) {
    return requestV1.deleteForm(`${prefix}/putsite/delete/one/${data}`);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/putsite/delete/batch/${data}`);
}