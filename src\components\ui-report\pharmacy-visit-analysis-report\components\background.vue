<template>
  <div class="background-page-v6">
    <template v-for="page in pageContent">
      <div
        class="background-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="background-page-top"></div>
        <div class="background-page-t">{{ subTitle }}</div>
        <div class="background-page-c" :id="page.uuid" v-if="pagetype === 4">
          {{ content }}
        </div>
        <template v-else>
          <div
            class="background-page-c"
            :id="page.uuid"
            v-html="page.innerHtml"
          ></div>
        </template>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  data() {
    return {
      pageUuid: "pharmacyVisitAnalysisReportBackground",
      subTitle: "",
      uuidKey: "background",
      pageContent: [
        {
          type: "background",
          uuid: "background-page_0",
          children: [],
          innerHtml: "",
        },
      ],
      content: "",
    };
  },
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    openInner: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    moreAnalysisPage(moreNodes, idx) {
      idx += 1;
      this.pageContent.splice(idx, 0, {
        text: "调研结果分析",
        type: "research-conclusion",
        uuid: this.getUuid(),
        pageContent: {
          contentHtml: "",
        },
      });
      let parentNode = document.createElement("div");
      for (let i = 0; i < moreNodes.length; i++) {
        parentNode.appendChild(moreNodes[i]);
      }
      let innerhtml = parentNode.innerHTML;
      console.log(innerhtml, "innerHtml");
      this.dymamicsTextContent(idx, innerhtml);
    },
    async dymamicsTextContent(cidx, html) {
      this.pageContent[cidx].innerHtml = html;
      await this.$nextTick();
      let uuid = this.pageContent[cidx].uuid;
      let dom = document.getElementById(uuid);
      let children = dom.children;
      let is = true;
      let moreNodes = [];
      console.log("children", children);
      for (let i = 0; i < children.length; i++) {
        let ct = children[i].offsetTop;
        let ch = children[i].offsetHeight;
        console.log("ct=ch", ct, ch);
        if (ct + ch > this.boxHeight) {
          console.log(
            children[i],
            children[i].innerHTML,
            children[i].childNodes
          );
          let childNodes = children[i].childNodes;
          var computedStyle = window.getComputedStyle(children[i]);
          var lineHeight =
            computedStyle.getPropertyValue("line-height").split("px")[0] - 0;
          var clonedElement = children[i].cloneNode();
          let ccount = 0;
          for (let k = 0; k < childNodes.length; k++) {
            ccount += lineHeight;
            if (ccount + ct > this.boxHeight) {
              let b = childNodes[k].cloneNode(true);
              clonedElement.appendChild(b);
              children[i].removeChild(childNodes[k]);
              k -= 1;
            }
          }
          moreNodes.push(clonedElement);
        }
      }
      if (moreNodes.length > 0) {
        console.log("moreNodes", moreNodes);
        this.moreAnalysisPage(moreNodes, cidx);
      }
    },
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle, content, uuidKey } = pageObject;
      this.subTitle = subTitle;
      this.content = content;
      if (uuidKey) {
        this.uuidKey = uuidKey;
      }
      if (this.pageContent.length !== 0) {
        this.pageContent[0].uuid = this.uuidKey + "_0";
      }
      await this.$nextTick();
      if (this.openInner) {
        this.trimSuccess();
      } else {
        await this.dymamicsTextContent(0, content);
        await this.$nextTick();
        this.trimSuccess();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.background-page-v6 {
  background: #fff;
  .background-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 103px 40px 20px;
  }
  .background-page-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .background-page-t {
    margin-top: 43px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 24px;
    background-size: 100% 100%;
  }
  .background-page-c {
    font-weight: 500;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
  }
}
</style>