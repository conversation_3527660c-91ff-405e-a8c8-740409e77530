import request from '@/common/utils/modules/request';
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'

let prefix = '/sop/api'
//查询共享仓换货分页数据
export function queryShareReplaceStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStoreRecord/queryShareReplaceStorePage', data);
}

//查询共享仓入库分页数据
export function queryShareInStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStoreRecord/queryShareInStorePage', data);
}

//查询共享仓出库分页数据
export function queryShareOutStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStoreRecord/queryShareOutStorePage', data);
}

//查询客户仓入库分页数据
export function queryCustomerInStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStoreRecord/queryCustomerInStorePage', data);
}

//查询客户仓出库分页数据
export function queryCustomerOutStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStoreRecord/queryCustomerOutStorePage', data);
}

// 自营仓库统计
export function getInStoreStatistics(data) {
  return requestV1.get(prefix + '/storehouseStoreRecord/getInStoreStatistics', data)
}

// 客户共享仓库汇总表
export function getShareStoreStatistics(data) {
  return requestV1.get(prefix + '/storehouseStoreRecord/getShareStoreStatistics', data)
}

// 自营仓库 出库分页数据
export function querySelfOutStorePage(data) {
  return requestV1.postJson(`${prefix}/storehouseStoreRecord/querySelfOutStorePage`, data)
}

// 获取客户最新一条出库记录
export function getCustomerLastSelfOutRecord(data) {
  return requestV1.get(`${prefix}/storehouseStoreRecord/getCustomerLastSelfOutRecord`, data)
}

// 自营调拨 
export function storehouseStoreRecordTransfer(data) {
  return requestV1.putJson(`${prefix}/storehouseStoreRecord/transfer`, data)
}

// 自营入库导入
export const importExcel = env.ctx + prefix + '/storehouseStoreRecord/self/store/import'
