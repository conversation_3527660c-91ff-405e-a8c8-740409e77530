/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//分页查询
export function queryPage(data) {
      return requestV1.postJson(prefix+'/ReplenishPackageApply/queryPage', data);
}

//添加审核备忘录
export function addAuditComment(data) {
    return requestV1.postJson(prefix+'/ReplenishPackageApply/addAuditComment', data);
}

//更新发货信息
export function updateByDelivery(data) {
     return requestV1.putJson(prefix+'/ReplenishPackageApply/updateByDelivery', data);
}
