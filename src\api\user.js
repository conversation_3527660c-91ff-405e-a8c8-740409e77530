import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
let prefix = '/manage/api'

//用户解除绑定公众号用户
export function unbindWxMpUser(data) {
    return requestV1.postJson(prefix + '/user/unbindWxMpUser', data);
}

export function createUserQrCode(data) {
    // return requestV1.download(env.ctx + prefix + '/user/createUserQrCode?path=' + encodeURIComponent(data),{fileName:'二维码'},'二维码')
  return requestV1.get(prefix +  '/user/createUserQrCode?path=' + encodeURIComponent(data), {});
}

//更新密码
export function updatePassword(data) {
    return requestV1.putJson(prefix + '/user/updatePassword', data);
}

//获取能够创建的用户角色
export function getAddUserType(data) {
    return requestV1.get(prefix + '/user/getAddUserType', data);
}
//查询所有用户
export function listUserAppletMenus(data) {
    return requestV1.get(prefix + '/user/listUserAppletMenus', data);
}


//查询所有用户
export function userQueryList(data) {
    return requestV1.postJson(prefix + '/user/queryList', data);
}

// 获取员工档案 user/getUserDetailById
export function getUserDetailById(data){
    return requestV1.get(prefix + '/user/getCurrentUser');

}
