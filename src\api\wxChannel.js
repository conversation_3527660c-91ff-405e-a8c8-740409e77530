/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
    //添加渠道数据
export function addChannel(data) {
    return requestV1.postJson(prefix + '/wxChannel/addChannel', data);
}

//删除渠道数据
export function deleteChannel(data) {
    return requestV1.deleteForm(prefix + '/wxChannel/deleteChannel', data);
}

//修改渠道数据
export function editChannel(data) {
    return requestV1.putJson(prefix + '/wxChannel/editChannel', data);
}

//导出渠道数据
export function exportCode(data) {
    return requestV1.getBlob(prefix + '/wxChannel/exportCode', data);
}

//渠道粉丝码列表
export function queryAccountPage(data) {
    return requestV1.postJson(prefix + '/wxChannel/queryAccountPage', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/wxChannel/queryPage', data);
}