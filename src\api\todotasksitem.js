/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 需求管理
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/todotasksitem/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/todotasksitem/insert`, data)
}

// 查询数量
export function queryCount (data) {
    return requestV1.postJson(`${prefix}/todotasksitem/query/count`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/todotasksitem/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`/dm/api/v2/todotasksitem/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/todotasksitem/query/page`, data)
}

// 根据多参数进行单一查询
export function queryParam (data) {
    return requestV1.get(`${prefix}/todotasksitem/query/param`, data)
}

// 任务进度
export function queryProgress (data) {
    return requestV1.get(`${prefix}/todotasksitem/query/progress`, data)
}

// 修改任务执行状态
export function updateDistribute (data) {
    return requestV1.putJson(`${prefix}/todotasksitem/update/distribute`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/todotasksitem/update`, data)
}

// 是否为最后一条需要确定的子项
export function isFinalConfirmedItem (data) {
    return requestV1.get(`${prefix}/todotasksitem/isFinalConfirmedItem`, data)
}

// 批量修改任务执行状态
export function updateDistributeBatch (data) {
    return requestV1.putJson(`${prefix}/todotasksitem/update/distribute/batch`, data)
}

// 根据userId查询用户未完成审核的子项列表
export function notCompleteByUserIdList (data) {
  return requestV1.get(`${prefix}/todotasksitem/notComplete/byUserId/list`, data)
}
