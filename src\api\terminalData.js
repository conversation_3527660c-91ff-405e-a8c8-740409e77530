import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//查询公众号吸粉详情总数
export function queryFansDetailCount(data) {
    return requestV1.postJson(prefix+'/terminalData/queryFansDetailCount', data);
}

//查询公众号吸粉详情
export function queryFansDetail(data) {
    return requestV1.postJson(prefix+'/terminalData/queryFansDetail', data);
}

//查询离线设备详情
export function queryOfflineDeviceDetail(data) {
   return requestV1.postJson(prefix+'/terminalData/queryOfflineDeviceDetail', data);
}

//统计经销商的出袋数据
export function listAllOperatorData(data) {
    return requestV1.postJson(prefix+'/terminalData/listAllOperatorData', data);
}

//按时间统计出袋数据
export function listOutPackageLineData(data) {
  return requestV1.postJson(prefix+'/terminalData/listOutPackageLineData', data);
}

//面板统计数据
export function countTerminalData(data) {
      return requestV1.postJson(prefix+'/terminalData/countTerminalData', data);
}

// 统计总出袋数据
export function terminalDataCountPacket(data) {
  return requestV1.postJson(prefix+'/terminalData/countPacket', data);
}

// 面板统计设备数据
export function terminalDataCountDevice(data) {
  return requestV1.postJson(prefix+'/terminalData/countDevice', data);
}

// 面板统计粉丝数据
export function terminalDataCountFans(data) {
  return requestV1.postJson(prefix+'/terminalData/countFans', data);
}


