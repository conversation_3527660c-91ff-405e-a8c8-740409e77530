/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 用工档案
 */

// 批量更改认证状态
export function batchUpdateStatus (data) {
    return requestV1.putJson(`${prefix}/physicianinfo/batchUpdateStatus`, data);
}

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/physicianinfo/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/physicianinfo/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/physicianinfo/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/physicianinfo/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/physicianinfo/query/page`, data)
}

// 根据userId获取用工档案
export function queryOneByUserId (data) {
    return requestV1.get(`${prefix}/physicianinfo/queryOneByUserId`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/physicianinfo/update`, data)
}

// 档案导入
export const upload = `${env.ctx + prefix}/physicianinfo/upload`

// 查询当前已绑定的用工档案
export function queryPageBindDoctor(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/query/page/bindDoctor`, data)
}

// 用工档案绑定医生档案
export function bingDoctorFiles(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/bingDoctorFiles`, data)
}

// 用工档案解绑医生
export function unBingDoctorFiles(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/unBingDoctorFiles`, data)
}

// 审核状态更新
export function auditstatusUpdate(data) {
  return requestV1.putJson(`${prefix}/physicianinfo/auditstatus/update`, data)
}

// 审核状态更新批量
export function auditstatusUpdateBatch(data) {
  return requestV1.putJson(`${prefix}/physicianinfo/auditstatus/update/batch`, data)
}

// 批量同步慧用工
export function batchSynchronizationOfEmployment(data) {
  return requestV1.postForm(`${prefix}/allianceprovideruserlog/reSync/contract`, data)
}

// 同步档案到联盟
export function syncToAlliance(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/sync/to/alliance`, data)
}

// 同步更新档案到联盟
export function syncUpdateToAlliance(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/sync/update/to/alliance`, data)
}

// 同步更新档案到租户
export function syncTenant(data) {
  return requestV1.postJson(`${prefix}/physicianinfo/sync/tenant`, data)
}

// 批量发送短信催签
export function tenantSendSms(data) {
  return requestV1.postForm(`${prefix}/physicianinfo/tenant/send/sms`, data)
}
