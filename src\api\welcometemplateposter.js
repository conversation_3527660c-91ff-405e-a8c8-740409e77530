import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api/v1'

// 根据ID指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/welcometemplateposter/delete/one/${data.id}`)
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/welcometemplateposter/insert`, data)
}

// 根据欢迎语模板ID查询推送列表
export function queryList (data) {
    return requestV1.get(`${prefix}/welcometemplateposter/query/list`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/welcometemplateposter/update`, data)
}