import requestV1 from '@/common/utils/modules/request'

let prefix = '/adservice/api/v1'
    //根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/position/query/list', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/position/query/page', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/position/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/position/update', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/position/query/one', data);
}

//根据主键集合字符串批量删除数据
export function deleteBatch(data) {
    return requestV1.deleteForm(`${prefix}/position/delete/batch/${data}`);
}

//获取广告位已绑定素材规格
export function associate(data) {
    return requestV1.postJson(prefix + '/position/query/materialspecification/associate/position/page', data);
}

//获取广告位未绑定素材规格
export function notAssociate(data) {
    return requestV1.postJson(prefix + '/position/query/materialspecification/not/associate/position/page', data);
}

//关联素材规格
export function bind(data) {
    return requestV1.postJson(prefix + '/position/bind/materialspecificationposition', data);
}

//取消绑定广告位的素材规格
export function cancel(data) {
    return requestV1.deleteForm(`${prefix}/position/cancel/materialspecificationposition/${data}`);
}

//开始广告位
export function start(data) {
    return requestV1.putForm(prefix + '/position/start/plan', data);
}

//根据主键单一查询
export function stop(data) {
    return requestV1.putForm(prefix + '/position/stop/plan', data);
}