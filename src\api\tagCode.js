import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//更新标签缓存
export function updateAllCacheTagCode(data) {
     return requestV1.get(prefix+'/tagCode/updateAllCacheTagCode', data);
}


//修改标签数据
export function codeEdit(data) {
  return requestV1.putJson(prefix+'/tagCode/edit', data);
}

//删除标签数据列表
export function codeDelete(data) {
     return requestV1.postJson(prefix+'/tagCode/delete', data);
}

//根据父节点id查询标签表
export function findByParentId(data) {
    return requestV1.get(prefix+'/tagCode/findByParentId', data);
}

//添加标签数据
export function add(data) {
  return requestV1.postJson(prefix+'/tagCode/add', data);
}
