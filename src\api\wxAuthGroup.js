import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
    //根据授权类型获取正常状态广告主列表
export function listNormalWxAuthByType(data) {
    return requestV1.postJson(prefix + '/wxAuth/listNormalWxAuthByType', data);
}

//查询所有服务号授权数据
export function listWxAuthByType(data) {
    return requestV1.get(prefix + '/wxAuth/listWxAuthByType', data);
}

//添加员工到分组
export function editGroup(data) {
    return requestV1.putJson(prefix + '/wxAuthGroup/editGroup', data);
}

//添加员工到分组
export function addUserToGroup(data) {
    return requestV1.postJson(prefix + '/wxAuthGroup/addUserToGroup', data);
}

//删除分组
export function deleteGroup(data) {
    return requestV1.deleteForm(prefix + '/wxAuthGroup/deleteGroup', data);
}

//添加企业号分组
export function addGroup(data) {
    return requestV1.postJson(prefix + '/wxAuthGroup/addGroup', data);
}

//获取分组中的所有企业号成员
export function listGroupContactUser(data) {
    return requestV1.get(prefix + '/wxAuthGroup/listGroupContactUser', data);
}

//获取企业号的所有分组
export function listWxAuthAllGroup(data) {
    return requestV1.get(prefix + '/wxAuthGroup/listWxAuthAllGroup', data);
}