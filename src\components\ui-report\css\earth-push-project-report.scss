$mainColor: #41a8a4;
$bgColor: #ebfffe;
$bgtableHeader: #94cecc;
$bgTableBorder: #dbdbdb;
// $bgTableBorder: #474747;
$bgtableHeaderV2: #8cb7df;
$bgtableHeaderV3: #dcbb90;

.pull-new-details-v2,
.recruit-new-user-information-box-v2,
.topic-box-v2,
.pull-new-details-v3,
.invitation-registration-v3,
.invitation-registration-v3,
.science-popularization-v3 {
  .pull-table {
    .el-table::before {
      background-color: $bgTableBorder;
    }
  }

  .el-table {
    .pull-new-details-table-row th {
      background-color: $bgtableHeaderV2;
      color: #797e7e;
      border-top: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .pull-new-details-table-row th:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .el-table__row .pull-new-details-table-cell:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }
  }

  .pull-table th.is-leaf,
  .pull-table td {
    border-right: var(--borderWidth, 1px) solid $bgTableBorder;
    border-bottom: var(--borderWidth, 1px) solid $bgTableBorder;
  }
}

.pull-new-details-v5,
.recruit-new-user-information-box-v5,
.topic-box-v5 {
  .pull-table {
    .el-table {
      background-color: transparent;
    }

    .el-table::before {
      background-color: $bgTableBorder;
    }
  }

  .el-table {
    tr {
      background-color: transparent;
    }

    .pull-new-details-table-row th {
      background-color: $bgtableHeaderV3;
      color: #797e7e;
      border-top: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .pull-new-details-table-row th:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .el-table__row .pull-new-details-table-cell:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }
  }

  .pull-table th.is-leaf,
  .pull-table td {
    border-right: var(--borderWidth, 1px) solid $bgTableBorder;
    border-bottom: var(--borderWidth, 1px) solid $bgTableBorder;
  }
}

.topic-rect-page-v6 {
  .pull-table {
    .el-table::before {
      background-color: $bgTableBorder;
    }
  }

  .el-table {
    .pull-new-details-table-row th {
      background-color: $bgtableHeaderV2;
      // color: #797e7e;
      color: #666;
      border-top: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .pull-new-details-table-row th:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .el-table__row .pull-new-details-table-cell:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
      background-color: $bgtableHeaderV2;

      .cell {
        color: #666;
        font-weight: 700;
      }
    }
  }

  .pull-table th.is-leaf,
  .pull-table td {
    border-right: var(--borderWidth, 1px) solid $bgTableBorder;
    border-bottom: var(--borderWidth, 1px) solid $bgTableBorder;
  }

  .el-table__row:first-child {
    td {
      border-top: var(--borderWidth, 1px) solid $bgTableBorder;
    }
  }
}

.topic-rect-page-v2-v6,
.execution-personnel-data-page-v6,
.topic-rect-page-v3-v6,
.execution-details-page-v6,
.topic-rect-page-v4-v6 {
  .pull-table {
    .el-table::before {
      background-color: $bgTableBorder;
    }
  }

  .el-table {
    .pull-new-details-table-row th {
      background-color: $bgtableHeaderV2;
      // color: #797e7e;
      color: #666;
      border-top: var(--borderWidth, 1px) solid $bgTableBorder;
    }

    .pull-new-details-table-row th:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;

    }

    .el-table__row .pull-new-details-table-cell:first-child {
      border-left: var(--borderWidth, 1px) solid $bgTableBorder;
    }
  }

  .pull-table th.is-leaf,
  .pull-table td {
    border-right: var(--borderWidth, 1px) solid $bgTableBorder;
    border-bottom: var(--borderWidth, 1px) solid $bgTableBorder;
  }

}

.table-size14 {
  .cell {
    font-size: 14px;
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .pull-new-details-table-cell {
    .cell {
      color: #909090;
    }
  }
}

.table-size24 {
  .cell {
    font-size: 20px;
    padding: 15px 5px;
  }

  .pull-new-details-table-cell {
    .cell {
      color: #909090;
    }
  }
}

.table-size_noscale {
  .cell {
    font-size: 13px;
    padding: 5px 5px;
  }

  .pull-new-details-table-cell {
    .cell {
      color: #909090;
    }
  }
}

.table-size18 {

  .cell {
    font-size: 16px;
    padding: 10px;
  }

  .pull-new-details-table-row {
    .cell {
      // color: #363a3c;
      color: #666;
    }
  }

  .pull-new-details-table-cell {
    .cell {
      color: #909090;
    }
  }
}

.yaHei-family {
  font-family: "Microsoft YaHei", "微软雅黑";
}

.bottom-border {
  position: relative;

  .bottom-tb {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 23px;
    background-image: var(--border-tb);
    background-size: 100% 100%;
  }

  .bottom-bb {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 23px;
    background-image: var(--border-tb);
    background-size: 100% 100%;
  }

  .bottom-lb {
    position: absolute;
    width: 10px;
    top: 23px;
    bottom: 23px;
    left: 1px;
    background-image: var(--border-lr);
    background-size: 100% auto;
  }

  .bottom-rb {
    position: absolute;
    width: 10px;
    top: 23px;
    bottom: 23px;
    right:1px;
    background-image: var(--border-lr);
    background-size: 100% auto;
  }
}

.bottom-border-v2 {
  position: relative;
  .bottom-tb {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 46px;
    background-image: var(--border-tb-v2);
    background-size: 100% 100%;
  }

  .bottom-bb {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 46px;
    background-image: var(--border-bb-v2);
    background-size: 100% 100%;
  }

  .bottom-lb {
    position: absolute;
    width: 13px;
    top: 49px;
    bottom: 49px;
    left: -1px;
    background-image: var(--border-lr-v2);
    background-size: 100% auto;
  }

  .bottom-rb {
    position: absolute;
    width: 13px;
    top: 49px;
    bottom: 49px;
    right: 1px;
    background-image: var(--border-lr-v2);
    background-size: 100% auto;
  }
}