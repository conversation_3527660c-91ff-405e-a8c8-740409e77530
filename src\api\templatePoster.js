import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//设置排序
export function setSort(data) {
    return requestV1.postJson(prefix + '/templatePoster/setSort', data);
}

//预览发送
export function previewSend(data) {
    return requestV1.postJson(prefix + '/templatePoster/previewSend', data);
}

//根据posterId获取预览发送下拉框
export function getPreviewSendView(data) {
    return requestV1.get(prefix + '/templatePoster/getPreviewSendView', data);
}

//批量解绑与安装单位关联
export function batchRemoveUnit(data) {
    return requestV1.postJson(prefix + '/templatePoster/batchRemoveUnit', data);
}

//批量与安装单位关联
export function batchBindUnit(data) {
    return requestV1.postJson(prefix + '/templatePoster/batchBindUnit', data);
}

//查询关联安装单位分页数据
export function queryUnitPage(data) {
    return requestV1.postJson(prefix + '/templatePoster/queryUnitPage', data);
}

//修改(只需要传id和launchStatus)投放状态
export function updateLaunchStatus(data) {
    return requestV1.putJson(prefix + '/templatePoster/updateLaunchStatus', data);
}

//查询关联广告主分页数据
export function queryAuthPage(data) {
    return requestV1.postJson(prefix + '/templatePoster/queryAuthPage', data);

}

//批量解绑与广告主关联
export function batchRemoveAuth(data) {
    return requestV1.postJson(prefix + '/templatePoster/batchRemoveAuth', data);
}

//批量与广告主关联
export function batchBindAuth(data) {
    return requestV1.postJson(prefix + '/templatePoster/batchBindAuth', data);
}

//删除微信模板推送消息
export function deletes(data) {
    return requestV1.deleteJson(prefix + '/templatePoster/deletes', data);
}

//根据ID获取微信模板推送消息详情
export function findById(data) {
    return requestV1.get(prefix + '/templatePoster/findById', data);
}

//编辑微信模板推送消息
export function edit(data) {
    return requestV1.putJson(prefix + '/templatePoster/edit', data);
}

//新增微信模板推送消息
export function add(data) {
    return requestV1.postJson(prefix + '/templatePoster/add', data);
}

//查询模板消息推送表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/templatePoster/queryPage', data);
}