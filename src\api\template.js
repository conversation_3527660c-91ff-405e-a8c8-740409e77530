import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//配置模板消息
export function settingWxTempinfo(data) {
    return requestV1.postJson(prefix+'/template/settingWxTempinfo', data);
}

//根据ID获取微信模板消息详情
export function findById(data) {
      return requestV1.get(prefix+'/template/findById', data);
}


//编辑微信模板消息
export function edit(data) {
     return requestV1.putJson(prefix+'/template/edit', data);
}

//新增微信模板消息
export function add(data) {
    return requestV1.postJson(prefix+'/template/add', data);
}

//删除微信模板消息
export function deletes(data) {
  return requestV1.postJson(prefix+'/template/deletes', data);
}

//(下拉框)获取所有模板消息列表
export function getTempinfoList(data) {
  return requestV1.postJson(prefix+'/template/getTempinfoList', data);
}


//查询表格分页数据
export function queryPage(data) {
     return requestV1.postJson(prefix+'/template/queryPage', data);
}
