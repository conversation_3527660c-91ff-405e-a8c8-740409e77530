<template>
  <div
    class="project-execution-process-page-v6"
    :style="{
      '--button-bg': 'url(' + buttonBg + ')',
    }"
  >
    <template v-for="page in pageContent">
      <div
        class="project-execution-process-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="project-execution-process-top"></div>
        <div class="project-execution-process-t">{{ subTitle }}</div>
        <div class="project-execution-process-c">
          <template v-for="item in dictionaryResult">
            <div class="project-execution-process-ci" :key="item.label">
              <div class="flex1 left-item">
                <div class="project-execution-process-cl">
                  <template v-if="item.left">
                    <div
                      class="project-execution-process-c-il i-left"
                      v-if="item.right"
                    >
                      <span class="span-text">
                        {{ item.indent }}
                      </span>
                    </div>
                    <div class="project-execution-process-c-tl">
                      {{ item.label }}
                    </div>
                    <div
                      class="project-execution-process-c-il i-right"
                      v-if="item.left"
                    >
                      <span class="span-text">
                        {{ item.indent }}
                      </span>
                    </div>
                  </template>
                </div>
              </div>
              <div class="flex1 right-item">
                <div class="project-execution-process-cr">
                  <template v-if="item.right">
                    <div
                      class="project-execution-process-c-il i-left"
                      v-if="item.right"
                    >
                      <span class="span-text">
                        {{ item.indent }}
                      </span>
                    </div>
                    <div class="project-execution-process-c-tl">
                      {{ item.label }}
                    </div>
                    <div
                      class="project-execution-process-c-il i-right"
                      v-if="item.left"
                    >
                      <span class="span-text">
                        {{ item.indent }}
                      </span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      subTitle: "",
      uuidKey: "project-execution-process",
      dictionaryResult: [
        {
          label: "明确拜访目标",
          indent: "一",
          left: false,
          right: false,
          width: 207,
        },
        {
          label: "药店样本筛选与清单构建",
          indent: "一",
          left: false,
          right: false,
          width: 280,
        },
        {
          label: "拜访周期与行程规划",
          indent: "一",
          left: false,
          right: false,
        },
        {
          label: "制定沟通话术",
          indent: "一",
          left: false,
          right: false,
        },
        {
          label: "开展药店拜访",
          indent: "一",
          left: false,
          right: false,
        },
        {
          label: "记录拜访信息",
          indent: "一",
          left: false,
          right: false,
        },
        {
          label: "输出拜访总结",
          indent: "一",
          left: false,
          right: false,
        },
      ],
      pageContent: [
        {
          type: "project-execution-process",
          uuid: "project-execution-process_0",
          children: [],
        },
      ],
      buttonBg:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-execution-button-bg.png",
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      this.subTitle = pageObject.subTitle;
      let vidx = 1;
      this.dictionaryResult.forEach((item, itemIdx) => {
        if (vidx === 1) {
          item.right = true;
          item.left = false;
          vidx = 0;
        } else {
          item.right = false;
          item.left = true;
          vidx = 1;
        }
        let idx = itemIdx + 1;
        item.indent = idx < 10 ? "0" + idx : idx;
      });
      console.log("this.dictionaryResult", this.dictionaryResult);
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
.project-execution-process-page-v6 {
  background: #fff;
  .project-execution-process-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 133px 48px 20px;
  }
  .project-execution-process-top {
    height: 133px;
    width: 100%;
    background: var(--backgroud-top-bg-v2);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 10% 1%;
  }
  .project-execution-process-t {
    margin-top: 43px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 33px;
    background-size: 100% 100%;
  }
  .project-execution-process-c {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 44px;
  }
  .project-execution-process-ci {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
  }
  .project-execution-process-cl,
  .project-execution-process-cr {
    // flex: 1;
    display: inline-flex;
    align-items: center;
    // min-width: 207px;
    min-height: 57px;
    justify-content: space-between;
    position: relative;
  }
  .project-execution-process-cl .project-execution-process-c-tl {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .project-execution-process-cr .project-execution-process-c-tl {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .project-execution-process-cc {
    min-width: 72px;
    height: 100%;
  }
  .project-execution-process-c-il {
    width: 100px;
    height: 100px;
    background: var(--button-bg);
    position: absolute;
    border-radius: 50%;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 27px;
    color: $mainColor;
  }
  .i-left.project-execution-process-c-il {
    left: -20px;
    top: -20px;
    transform: translateX(-50%);
  }
  .i-right.project-execution-process-c-il {
    right: -20px;
    top: -20px;
    transform: translateX(50%);
  }
  .project-execution-process-c-tl {
    background: #cce6ff;
    flex: 1;
    height: 57px;
    display: flex;
    align-items: center;
    padding: 0 30px;
    box-sizing: border-box;
    color: $mainColor;
    border-radius: 30px;
    font-size: 19px;
  }
  .flex1 {
    flex: 1;
    // border: 1px solid #dbdbdb;
  }
  .left-item {
    display: flex;
    justify-content: flex-end;
    padding-right: 40px;
  }
  .right-item {
    padding-left: 40px;
  }
  .span-text {
    margin-top: -5px;
  }
  .project-execution-process-cl .project-execution-process-c-tl{
    justify-content: flex-end;
  }
}
</style>