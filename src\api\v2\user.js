/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/manage/api/user/v2'

//添加用户v2
export function addUser(data) {
    return requestV1.postJson(prefix + '/add/user', data);
}

//更新用户信息v2
export function editUser(data) {
    return requestV1.putJson(prefix + '/edit/user', data);
}

//获取用户v2
export function getByUser(data) {
    return requestV1.get(prefix + '/get/user/by/id', data);
}

//删除用户
export function deletes(data) {
    return requestV1.deleteForm(prefix + '/user/delete', data);
}

// 同步中央用户档案
export function syschronizationCenteruser (data) {
    return requestV1.postForm('/manage/api/user/syschronizationCenteruser', data)
}