/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/sop/api'
//发起财务认证
export function sendFinanceVerify(data) {
    return requestV1.postForm(prefix+'/surcharge/sendFinanceVerify', data);
}

//财务认证
export function financeVerify(data) {
    return requestV1.postForm(prefix+'/surcharge/financeVerify', data);
}

//查询表格分页数据
export function queryPage(data) {
      return requestV1.postJson(prefix+'/surcharge/queryPage', data);
}

//编辑附加费用信息
export function editCustomer(data) {
   return requestV1.postJson(prefix+'/surcharge/editCustomer', data);
}


//添加附加费用
export function addSurcharge(data) {
    return requestV1.postJson(prefix+'/surcharge/addSurcharge', data);
}

//批量删除附加费用信息
export function deletes(data) {
    return requestV1.deleteJson(prefix+'/surcharge/deletes', data);
}
