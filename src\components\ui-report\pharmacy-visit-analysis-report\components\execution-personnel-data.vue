<template>
  <div class="execution-personnel-data-page-v6">
    <template v-for="page in pageContent">
      <div
        class="execution-personnel-data-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="execution-personnel-data-top"></div>
        <div class="execution-personnel-data-t">{{ subTitle }}</div>
        <div class="execution-personnel-data-c">
          <div class="execution-personnel-data-ct">
            <div class="execution-personnel-data-ctl"></div>
            项目执行人的性别年龄分析
          </div>
          <div class="execution-personnel-data-ul">
            <div class="execution-personnel-data-l">
              <div class="execution-personnel-data-li">
                <div class="execution-personnel-data-li-i">
                  <img
                    :src="filePrex + boyIco"
                    class="execution-personnel-data-li-icon-boy"
                    alt=""
                  />
                  <div class="execution-personnel-data-li-tip">
                    男性{{ manObject.number }}人
                  </div>
                </div>
                <div class="w26"></div>
                <div class="execution-personnel-data-li-i">
                  <img
                    :src="filePrex + grilIco"
                    class="execution-personnel-data-li-icon-gril"
                    alt=""
                  />
                  <div class="execution-personnel-data-li-tip">
                    女性{{ womanObject.number }}人
                  </div>
                </div>
              </div>
              <div class="execution-personnel-data-text">
                项目执行人性别分布
              </div>
            </div>
            <div class="execution-personnel-data-r">
              <div
                class="execution-personnel-data-r-chart"
                id="execution-personnel-chart-uuid"
              ></div>
              <div
                class="xAxis-User"
                :style="{
                  '--echart-border': 'url(' + borderImg + ')',
                }"
              >
                <div
                  class="xAxis-item"
                  v-for="item in currentOption"
                  :key="item.name"
                >
                  {{ item.name }}
                </div>
              </div>
              <div class="execution-personnel-data-text">
                项目执行人年龄对比分析
              </div>
            </div>
          </div>
        </div>
        <div class="execution-personnel-data-footer bottom-border">
          <div class="bottom-tb"></div>
          <div class="bottom-bb"></div>
          <div class="bottom-lb"></div>
          <div class="bottom-rb"></div>
          根据统计信息:项目执行人共计{{ sexResultCount }}人，男性人数为{{
            manObject.number
          }}人，占比{{ manObject.percent }}。女性人数为{{
            womanObject.number
          }}人，占比{{ womanObject.percent }}，18-30周岁{{
            projectExecutorInformation.age_18_30_num
          }}人，占比{{ projectExecutorInformation.age_18_30 }},31-40周岁{{
            projectExecutorInformation.age_31_40_num
          }}人，占比{{ projectExecutorInformation.age_31_40 }},41-50周岁{{
            projectExecutorInformation.age_41_50_num
          }}人，占比{{ projectExecutorInformation.age_41_50 }}。
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";
export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      borderImg:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-echart-border.png",
      currentOption: [],
      subTitle: "",
      womanObject: {
        percent: "0%",
        number: 0,
      },
      manObject: {
        percent: "0%",
        number: 0,
      },
      sexResultCount: 0,
      projectExecutorInformation: {
        age_0_17: "0.00%",
        age_18_30: "0.00%",
        age_31_40: "0.00%",
        age_41_50: "0.00%",
        age_51_70: "0.00%",
        age_71_100: "0.00%",
        sex_1: "0.00%", // 男性
        sex_2: "0.00%", //女性
        sex_0: "0.00%", // 未知
      },
      borderWidth: null,
      uuidKey: "execution-personnel-data",
      boxWidth: this.pageSize.width - 80,
      tableWidth: this.pageSize.width - 80,
      filePrex:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/",
      grilIco: "icon-gril.png",
      boyIco: "icon-boy.png",
      pageContent: [
        {
          type: "execution-personnel-data-page",
          uuid: "execution-personnel-data-page_0",
          children: [],
          once: true,
        },
      ],
    };
  },
  methods: {
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    async initMethod() {
      let pageObject = this.pageObject || {};
      let subTitle = pageObject.subTitle;
      this.subTitle = subTitle;
      let userReportVo = pageObject.userReportVo;
      if (userReportVo.genderReportVoList instanceof Object) {
        let genderReportVoList = userReportVo.genderReportVoList;
        for (let i = 0; i < genderReportVoList.length; i++) {
          this.sexResultCount = genderReportVoList[i].allNum;
          let percent = genderReportVoList[i].proportion + "%";
          if (genderReportVoList[i].gender === 2) {
            // 女
            this.womanObject.percent = percent;
            this.womanObject.number = genderReportVoList[i].num;
          } else if (genderReportVoList[i].gender === 1) {
            // 男
            this.manObject.percent = percent;
            this.manObject.number = genderReportVoList[i].num;
          }
          let str = "sex_" + genderReportVoList[i].gender;
          this.projectExecutorInformation[str] = percent;
        }
      }
      if (userReportVo.ageReportVoList instanceof Object) {
        let ageReportVoList = userReportVo.ageReportVoList;
        // 18-30周岁
        let ageOption = [];
        for (let i = 0; i < ageReportVoList.length; i++) {
          let percent = ageReportVoList[i].proportion + "%";
          this.projectExecutorInformation[ageReportVoList[i].descValue] =
            percent;
          this.projectExecutorInformation[
            ageReportVoList[i].descValue + "_num"
          ] = ageReportVoList[i].num;
          let names = ageReportVoList[i].descValue.split("_");

          ageOption.push({
            name: names[1] + "-" + names[2] + "岁",
            value: ageReportVoList[i].num,
            uuid: ageReportVoList[i].descValue,
            selectOptionProportion: ageReportVoList[i].proportion,
          });
        }
        this.ageOption = ageOption;
      }

      await this.$nextTick();
      this.trimSuccess();
    },
    async initRender(list = []) {
      console.log("this.pageHeight", this.pageHeight);
      let cnum = 1;
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        item.cnum = cnum;
        cnum += 1;
        await this.rendCurrent(item, {
          type: "execution-personnel-data-page",
          once: this.pageContent.length === 0,
          children: [],
        });
      }
      this.trimSuccess({});
    },
    initRenderChart() {
      let pageObject = this.pageObject || {};
      let seriesDataOne = this.ageOption.filter((item) =>
        ["age_18_30", "age_31_40", "age_41_50"].includes(item.uuid)
      );
      this.currentOption = seriesDataOne;
      let chartOne = {
        belongType: 30,
        seriesData: seriesDataOne,
      };
      let chartUuidOne = "#execution-personnel-chart-uuid";
      if (seriesDataOne.length > 0) {
        this.initBarChart2D(chartOne, chartUuidOne, {
          yAxisOption: {
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          xAxisOption: {
            axisLabel: {
              fontSize: 16,
              color: "#393939",
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
          },
          labelOption: {
            formatter: "{c}人",
            fontWeight: "bold",
            fontSize: 14,
            // fontSize:
          },
        });
      }
    },
  },
  watch: {
    updatecount(n) {
      this.pageHeight = this.pageSize.height - 63;
      this.initMethod();
    },
    renderUpdateCount(n) {
      this.initRenderChart();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
$borderColor: #70a5d7;
.execution-personnel-data-page-v6 {
  background: #fff;
  .execution-personnel-data-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 73px 40px 20px;
  }
  .execution-personnel-data-top {
    height: 73px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 8% 0%;
  }
  .execution-personnel-data-t {
    margin-top: 44px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 27px;
    background-size: 100% 100%;
  }
  .execution-personnel-data-c {
  }
  .execution-personnel-data-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
  }
  .execution-personnel-data-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .execution-personnel-data-ul {
    display: flex;
    height: 313px;
  }
  .execution-personnel-data-l,
  .execution-personnel-data-r {
    flex: 1;
    position: relative;
  }
  .execution-personnel-data-l {
  }
  .execution-personnel-data-li {
    display: flex;
    justify-content: center;
    padding-top: 127px;
    height: 287px;
    box-sizing: border-box;
  }
  .execution-personnel-data-li-i {
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .execution-personnel-data-li-icon-boy {
    width: 53px;
    height: 60px;
    margin-bottom: 20px;
  }
  .execution-personnel-data-li-icon-gril {
    width: 65px;
    height: 59px;
    margin-bottom: 20px;
  }
  .execution-personnel-data-li-tip {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 1.5;
  }
  .execution-personnel-data-r-chart {
    height: 287px;
    width: 100%;
    // background: $mainColor;
  }
  .w26 {
    width: 56px;
    height: 100%;
  }
  .execution-personnel-data-text {
    display: flex;
    justify-content: center;
    font-weight: 550;
    font-size: 19px;
    color: #000000;
  }
  .execution-personnel-data-footer {
    // border: 1px dashed $borderColor;
    padding: 33px 30px;
    margin-top: 72px;
    line-height: 1.5;
    // font-weight: 500;
    font-size: 19px;
    color: #333333;
  }
  .mgt19 {
    padding-top: 19px;
  }
  .space-height {
    height: 27px;
    width: 100%;
  }
  .xAxis-User {
    width: 280px;
    position: absolute;
    display: flex;
    padding-top: 23px;
    top: 223px;
    left: 40px;
    &::before {
      content: "*";
      display: block;
      font-size: 0;
      position: absolute;
      top: 2px;
      left: 15px;
      right: 15px;
      height: 13px;
      background-image: var(--echart-border);
    }
    .xAxis-item {
      color: #333333;
    }
    .xAxis-item:first-child {
      margin-left: 18px;
    }
    .xAxis-item:nth-child(2) {
      margin-left: 36px;
    }
    .xAxis-item:nth-child(3) {
      margin-left: 38px;
    }
  }
}
</style>