/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
let prefix = '/sop/api'
//认证撤回
export function updateWithdrawStatus(data) {
  return requestV1.postForm(prefix + '/promotePayInfo/updateWithdrawStatus', data);

  // return request({
  //       url: '/api/promotePayInfo/updateWithdrawStatus',
  //       method: 'POST',
  //       data: qs.stringify(data),
  //       baseURL
  //   })
}

    //发起认证(发送认证消息)
export function sendVerifyMsg(data) {
    return requestV1.postForm(prefix + '/promotePayInfo/sendVerifyMsg', data);
}

//查询list数据
export function queryList(data) {
    return requestV1.postJson(prefix + '/promotePayInfo/queryList', data);
}

//财务认证
export function financeVerify(data) {
    return requestV1.postJson(prefix + '/promotePayInfo/financeVerify', data);
}

//注销推广款项信息
export function deletes(data) {
    return requestV1.deleteJson(prefix + '/promotePayInfo/deletes', data);
}

//编辑推广款项信息
export function edit(data) {
    return requestV1.putJson(prefix + '/promotePayInfo/edit', data);
}

//新增推广款项信息
export function add(data) {
    return requestV1.postJson(prefix + '/promotePayInfo/add', data);
}

//根据ID获取推广款项信息
export function findById(data) {
    return requestV1.get(prefix + '/promotePayInfo/findById', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/promotePayInfo/queryPage', data);
}
