import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 财务管理-我的钱包
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/mywallet/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/mywallet/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.postJson(`${prefix}/mywallet/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/mywallet/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/mywallet/query/page`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/mywallet/update`, data)
}

// 查看银行卡配置
export function queryWithdrawalConfig (data) {
    return requestV1.get(`${prefix}/mywallet/query/withdrawal/config`, data)
}

// 保存银行卡配置
export function withdrawalConfigInsert (data) {
    return requestV1.postJson(`${prefix}/mywallet/withdrawal/config/insert`, data)
}

// 批量保存银行卡配置（批量发起签约）
export function withdrawalBatchSaveConfig (data) {
    return requestV1.postJson(`${prefix}/mywallet/withdrawal/batch/save/config`, data)
}
// 保存银行卡配置
export function withdrawalConfigUpdateWalletInfo (data) {
  return requestV1.postJson(`${prefix}/financewithdrawal/update/wallet/info`, data)
}
