import requestV1 from '@/common/utils/modules/request'

let prefix = '/adservice/api/v1'
    //分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/weboriginality/query/page', data);
}

//根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/weboriginality/query/list', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/weboriginality/insert', data);
}

//更新数据
export function update(data) {
    return requestV1.putJson(prefix + '/weboriginality/update', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/weboriginality/query/one', data);
}

//根据主键id指定删除
export function deleteOne(data) {
    return requestV1.deleteForm(`${prefix}/weboriginality/delete/one/${data}`);
}