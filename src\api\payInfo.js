/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/sop/api'

//认证撤回
export function updateWithdrawStatus(data) {
    return requestV1.postForm(prefix + '/payInfo/updateWithdrawStatus', data);

    // return request({
    //       url: '/api/payInfo/updateWithdrawStatus',
    //       method: 'POST',
    //       data: qs.stringify(data),
    //       baseURL
    //   })
}

//查询表格数据
export function queryPage(data) {
    return requestV1.postJson(prefix + '/payInfo/queryPage', data);

    // return request({
    //       url: '/api/payInfo/queryPage',
    //       method: 'POST',
    //       data,
    //       baseURL
    //   })
}

//查询list数据
export function queryList(data) {
    return requestV1.postJson(prefix + '/payInfo/queryList', data);
}

//款项认证
export function financeVerify(data) {
    return requestV1.postJson(prefix + '/payInfo/financeVerify', data);
}

// 发起认证
export function sendVerifyMsg(data) {
    return requestV1.postForm(prefix + '/payInfo/sendVerifyMsg', data);
}

export function deletes(data) {
    return requestV1.deleteJson(prefix + '/payInfo/deletes', data);
}

export function findById(data) {
    return requestV1.get(prefix + '/payInfo/findById', data);
}
export function add(data) {
    return requestV1.postJson(prefix + '/payInfo/add', data);
}
export function edit(data) {
    return requestV1.postJson(prefix + '/payInfo/edit', data);
}