<template>
  <el-dialog
    title="云服务导出记录"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="false"
  >
    <div v-loading="dialogLoading">
      <searchList :from-data="selectFrom">
        <div slot="btnList" style="margin-bottom: 20px" class="btns">
          <el-button
            size="mini"
            icon="el-icon-search"
            type="primary"
            class="btn"
            @click="search()"
            >查询</el-button
          >
          <template v-if="[12, 13, 10, 14, 16, 17, 18, 19, 4, 3].includes(pagetype)">
            <enhanceButton
              v-bind="$props"
              @query="queryEnhance"
              style="margin-right: 20px"
            ></enhanceButton>
          </template>
          <template v-else>
            <el-button
              type="primary"
              size="mini"
              class="btn"
              @click="createLog()"
              >发起生成</el-button
            >
            <el-button
              size="mini"
              type="primary"
              class="btn"
              @click="previewDocx"
            >
              预览报告
            </el-button>
          </template>
          <el-button
            type="primary"
            size="mini"
            class="btn"
            @click="search(true)"
            >刷新</el-button
          >
          <el-button
            type="danger"
            size="mini"
            class="btn"
            @click="multDel()"
            :loading="mulDelLoading"
            >批量删除</el-button
          >
          <batchDownloadFile
            v-if="[12, 14, 16, 17, 20, 22].includes(pagetype)"
            style="margin-bottom: 0"
            :files="downloadFiles"
          ></batchDownloadFile>
        </div>
      </searchList>

      <div class="tabel">
        <tab
          :table-data="tableData.records"
          :loading="loading"
          :pagetype="pagetype"
          @select="select"
          @showTab="showTab"
        />
        <pagination
          :current="current"
          :size="size"
          :total="tableData.total"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        />
      </div>
      <a :href="aHref" ref="aRef" target="_blank"></a>
    </div>
    <!-- <span>这是一段信息</span> -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="close" size="mini">取 消</el-button>
      <el-button type="primary" @click="close" size="mini">确 定</el-button>
    </span>
  </el-dialog>
</template>


<script>
import tab from "./table.vue";
import pagination from "@/components/MyPagination";

// 服务器端导出
import {
  queryPageV2 as queryPage,
  exporttasklogUpdateExecuteStatus,
  deleteBatch,
  queryPageV3
} from "@/service/api/modules/dm/exporttasklog";
import { domainURL, format, getIOSTime, getFromData } from "@/utils";
import {
  getDmReportExecuteStatus,
  getDmReportExportBusinessType,
  getReportStyleEnum,
} from "@/utils/enumeration";
import batchDownloadFile from "@/components/batchDownloadFile/index.vue";
import { queryList as dmDemandQueryList } from "@/api/dmDemand";
import commonMixin from "./mixin.js";
import enhanceButton from "./enhance-button/index.vue";
export default {
  mixins: [commonMixin],
  components: {
    tab,
    pagination,
    batchDownloadFile,
    enhanceButton,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    // 导出文件名
    exportName: {
      type: [String, Number],
      default: new Date().getTime() + ".pdf",
    },
  },
  watch: {
    show(n) {
      this.visible = n;
      if (n) {
        if ([12, 14, 16, 17, 20, 22].includes(this.pagetype)) {
          this.dmDemandQueryList();
        }
        this.search();
      } else {
        // this.dialogLoading = true;
        this.selectArr = [];
      }
    },
  },
  data() {
    return {
      dialogLoading: false,
      aHref: "",
      downloadName: "",

      visible: false,
      tableData: {},
      current: 1,
      size: 10,
      loading: false,
      selectArr: [],
      mulDelLoading: false,
    };
  },
  methods: {
    getReportStyle(url = "") {
      let str = url + "";
      let reportStyle = 1;
      if (str.indexOf("reportStyle=2") !== -1) {
        console.log("包含了=========");
        reportStyle = 2;
      }
      console.log("reportStyle", reportStyle);
      return reportStyle;
    },
    queryEnhance(type) {
      console.log("closeDialog=======", type);
      this.search(true);
    },
    async dmDemandQueryList() {
      const res = await dmDemandQueryList();
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.title,
        };
      });
      let idx = this.selectFrom.findIndex((item) => item.id === "demandId");
      if (idx !== -1) {
        this.selectFrom[idx].option = data;
      }
    },
    getTargetName(businessType) {
      let n;
      switch (businessType) {
        case 16:
          n = "关于小葫芦平台的线上用户活动项目执行明细";
          break;
        case 17:
          n = "关于小葫芦平台的线下用户活动项目执行明细";
          break;
      }
      return n;
    },
    getFileName(row) {
      let filename = "";
      if (row.businessType === 12) {
        if (
          !this.$validate.isNull(row.userName) ||
          !this.$validate.isNull(row.taskName)
        ) {
          filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          filename = "小葫芦精准地推活动执行明细";
        } else {
          filename = new Date().getTime();
        }
      } else if (row.businessType === 14) {
        if (
          !this.$validate.isNull(row.userName) ||
          !this.$validate.isNull(row.taskName)
        ) {
          filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          filename = "小葫芦线上推广活动执行明细";
        } else {
          filename = new Date().getTime();
        }
      } else if ([16, 17,20].includes(row.businessType)) {
        if (
          !this.$validate.isNull(row.userName) ||
          !this.$validate.isNull(row.taskName)
        ) {
          filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          filename = this.getTargetName(row.businessType);
        } else {
          filename = new Date().getTime();
        }
      } 
      return filename;
    },
    multDel() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("必须选中一条数据");
      }
      this.$confirm("此操作将批量删除记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.mulDelLoading = true;
        const res = await deleteBatch({
          ids: this.selectArr.map((item) => item.id),
        }).catch((e) => {
          this.mulDelLoading = false;
        });
        this.mulDelLoading = false;
        this.$eltool.successMsg(res.msg);
        this.search();
      });
    },
    async createLog() {
      this.init();
    },
    getBusinessId() {
      let businessId = this.selectIds[0].businessId;
      let temp = businessId + "";
      let arr = temp.split("to");
      let str1 = format(Number(arr[0]), "YYYY-MM-DD").split("-").join("");
      let str2 = format(Number(arr[1]), "YYYY-MM-DD").split("-").join("");
      return str1 + str2;
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    search(noreset) {
      if (!noreset) {
        this.size = 10;
        this.current = 1;
      }
      this.loading = true;
      const size = this.size;
      const current = this.current;
      let businessId = this.selectIds[0].businessId;
      if (this.pagetype === 15) {
        // 陪诊
        businessId = this.getBusinessId();
      } else if (
        typeof businessId === "string" &&
        businessId.indexOf("-") !== -1
      ) {
        businessId = businessId.split("-").join("") - 0;
      } else if ([12, 14, 16, 17].includes(this.pagetype)) {
        // 个人报告不传
        businessId = null;
      }
      let condition2 = getFromData(this.selectFrom);
      let condition = {
        businessType: this.pagetype,
        businessId: businessId,
        ...condition2,
      };
      if (Array.isArray(condition.time)) {
        delete condition.startTime;
        delete condition.endTime;
        condition.startCreateTime = condition.time[0];
        condition.endCreateTime = condition.time[1];
      }
      if (this.noBusinessId) {
        // 不要businessId
        delete condition.businessId;
      }
      let api = queryPage;
      // 项目报告入口
      let pagetypeVal = Number(this.pagetype);
      if([10,11,13,15,18,19,21].includes(pagetypeVal)) {
        api = queryPageV3;
        condition.type = 0; // 只返回当前创建人的数据 1 所有数据
      }

      api({ size, current, condition })
        .then((res) => {
          const getDmReportExportBusinessTypeList =
            getDmReportExportBusinessType();
          res.data.records = res.data.records.map((item) => {
            let reportStyle = this.getReportStyle(item.exportUrl);
            return {
              ...item,
              reportStyle,
              reportStyleText: this.getEnumText(
                reportStyle,
                getReportStyleEnum()
              ),
              businessTypeText: this.getEnumText(
                item.businessType,
                getDmReportExportBusinessTypeList
              ),
              createTimeText: getIOSTime(item.createTime),
              generateTimeText: getIOSTime(item.generateTime),
              // businessText:
              //   item.businessType === 2 ?  item.taskName + '-' + item.physicianInfoName : item.taskName,
              executeStatusText: this.getEnumText(
                item.executeStatus,
                getDmReportExecuteStatus()
              ),
              monthText: this.getMonthText(item),
            };
          });
          this.tableData = res.data;
        })
        .then((res) => {
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
    getMonthText(item) {
      if ([10, 11, 13, 18, 19].includes(item.businessType)) {
        let str = item.businessId + "";
        return str.slice(0, 4) + "年" + str.slice(4) + "月";
      } else if ([15].includes(item.businessType)) {
        let str = item.businessId + "";
        return (
          str.slice(0, 4) +
          "年" +
          str.slice(4, 6) +
          "月" +
          str.slice(6, 8) +
          "日至" +
          str.slice(8, 12) +
          "年" +
          str.slice(12, 14) +
          "月" +
          str.slice(14) +
          "日"
        );
      } else {
        return "";
      }
    },
    showTab({ type, row }) {
      console.log("type", type);
      switch (type) {
        case 1:
          exporttasklogUpdateExecuteStatus({
            ids: row.id,
          }).then((res) => {
            this.$eltool.successMsg(res.msg);
            this.search(true);
          });
          // }
          break;
        case 2:
          // 下载
          this.aHref = row.filePath;
          this.downloadA(row);
          break;
        default:
      }
    },
    downloadA(row) {
      // 预览报告
      let location = window.location;
      let params = {
        url: this.aHref,
        businessType: row.businessType,
      };
      if (row.businessType === 12) {
        if (!this.$validate.isNull(row.userName)) {
          // params.filename = row.taskName + '_' + row.userName + '_小葫芦精准地推活动执行明细'
          params.filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          params.filename = "小葫芦精准地推活动执行明细";
        } else {
          params.filename = this.exportName;
        }
      } else if (row.businessType === 14) {
        if (!this.$validate.isNull(row.userName)) {
          params.filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          params.filename = "小葫芦线上推广活动执行明细";
        } else {
          params.filename = this.exportName;
        }
      } else if ([16, 17].includes(row.businessType)) {
        console.log("row===", row);
        if (
          !this.$validate.isNull(row.userName) ||
          !this.$validate.isNull(row.taskName)
        ) {
          params.filename = row.userName + "-" + row.taskName;
        } else if (!this.$validate.isNull(row.businessId)) {
          params.filename = this.getTargetName(row.businessType);
        } else {
          params.filename = this.exportName;
        }
      } else if (this.exportName) {
        params.filename = this.exportName;
      }
      let str = "";
      for (let key in params) {
        if (str === "") {
          str += key + "=" + params[key];
        } else {
          str += "&" + key + "=" + params[key];
        }
      }

      this.aHref =
        location.origin +
        location.pathname +
        "#" +
        `/servePrint/download?${str}`;

      this.$nextTick(() => {
        this.$refs.aRef.click();
      });
    },
    select(val) {
      this.selectArr = val;
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(query) {
      this.$emit("close", query);
    },
    query() {},
    handleSizeChange(val) {
      this.size = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.current = val;
      this.search(true);
    },
  },
  computed: {
    downloadFiles() {
      return this.selectArr
        .filter((item) => item.executeStatus === 3)
        .map((item) => {
          return {
            url: domainURL(item.filePath),
            filename: this.getFileName(item),
          };
        });
    },
    selectFrom(n) {
      n = [];
      if ([12, 14, 16, 17, 20, 22].includes(this.pagetype)) {
        n = [
          {
            title: "所属任务",
            id: "todoTaskTitle",
            value: null,
            type: 1,
            option: [],
          },
          {
            title: "所属用户",
            id: "userName",
            value: null,
            type: 1,
            option: [],
          },
          {
            title: "所属项目",
            id: "demandId",
            value: null,
            type: 2,
            option: [],
          },
          {
            title: "创建时间",
            id: "time",
            value: null,
            option: [],
            type: 6,
            defaultTime: ["00:00:00", "23:59:59"],
            dateType: "datetimerange",
          },
        ];
      }
      return n;
    },
  },
};
</script>


<style lang="scss" scoped>
.btns {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .btn {
    margin-right: 20px;
    margin-left: 0;
  }
}
</style>