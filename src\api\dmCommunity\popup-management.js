/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 小葫芦弹窗管理
 */

// 弹窗广告 - 分页
export function advertisementmanagementQueryPage (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagement/query/page`,data)
}

// 弹窗广告 - 新增
export function advertisementmanagementInsert (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagement/insert`,data)
}

// 弹窗广告 - 编辑
export function advertisementmanagementUpdate (data) {
  return requestV1.putJson(`${prefix}/advertisementmanagement/update`,data)
}

// 弹窗广告 - 删除
export function advertisementmanagementDeleteOne (data) {
  return requestV1.deleteForm(`${prefix}/advertisementmanagement/delete/one/${data.id}`)
}

// 弹窗广告 - 批量修改状态
export function advertisementmanagementBatchUpdateOpenStatus (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagement/batch/update/openStatus`,data)
}

// 弹窗广告 - 详情
export function advertisementmanagementQueryOne (data) {
  return requestV1.get(`${prefix}/advertisementmanagement/query/one`,data)
}

// 弹窗广告 - 统计
export function advertisementmanagementrecordStatistics (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagementrecord/query/one/statistics`,data)
}

// 弹窗广告 - 数据统计 - 导出
export function advertisementmanagementrecordExportStatistics (data) {
  // return requestV1.postJson(`${prefix}/advertisementmanagementrecord/export/statistics`,data)
  return requestV1.download(`${prefix}/advertisementmanagementrecord/export/statistics`, data, `弹窗数据统计.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// 弹窗广告 - 操作流水
export function advertisementmanagementitemrecordQueryPage (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagementitemrecord/query/page`,data)
}

// 弹窗广告 - 操作流水 - 统计
export function advertisementmanagementitemrecordStatistics (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagementitemrecord/statistics`,data)
}

// 弹窗广告 - 操作流水 - 导出
export function advertisementmanagementitemrecordExportStatistics (data) {
  return requestV1.download(`${prefix}/advertisementmanagementitemrecord/export/statistics`, data, `弹窗操作流水.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// h5功能统计
export function imagehomepageQueryPageStatistics (data) {
  return requestV1.postJson(`${prefix}/imagehomepage/query/page/statistics`,data)
}

export function imagehomepageExportExcel (data) {
  // return requestV1.postJson(`${prefix}/advertisementmanagementrecord/export/statistics`,data)
  return requestV1.download(`${prefix}/imagehomepage/export/excel`, data, `h5功能数据统计.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// h5功能统计 图片点击(uv pv) 曝光量
export function imagehomepageQueryStatistics (data) {
  return requestV1.postJson(`${prefix}/imagehomepage/query/statistics`,data)
}

// 弹窗广告 - 数据合计
export function advertisementmanagementStatisticsUseTypePage (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagement/statistics/useType/page`,data)
}

// 数据合计 - 导出
export function advertisementmanagementStatisticsExportExcel (data) {
  // return requestV1.postJson(`${prefix}/advertisementmanagementrecord/export/statistics`,data)
  return requestV1.download(`${prefix}/advertisementmanagement/statistics/export/excel`, data, `数据合计.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// 弹窗广告 - 数据合计 - 全部统计
export function advertisementmanagementStatisticsAll (data) {
  return requestV1.postJson(`${prefix}/advertisementmanagement/statistics/all`,data)
}