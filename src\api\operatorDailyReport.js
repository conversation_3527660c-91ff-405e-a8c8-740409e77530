/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//查询表格分页数据-详细
export function queryDetailPage(data) {
  return requestV1.postJson(prefix+'/operatorDailyReport/queryDetailPage', data);
}

//生成报表数据
export function createReport(data) {
  return requestV1.postForm(prefix+'/operatorDailyReport/createReport', data);
}

//查询表格分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix+'/operatorDailyReport/queryPage', data);
}
