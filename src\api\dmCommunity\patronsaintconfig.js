/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 健康保护神-基础配置
 */

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/patronsaintconfig/insert`, data)
}

// 查询倒序第一条数据
export function queryOnlyOne (data) {
    return requestV1.get(`${prefix}/patronsaintconfig/query/only/one`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/patronsaintconfig/update`, data)
}