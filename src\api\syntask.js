import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//启动同步
export function startSynch(data) {
      return requestV1.get(prefix+'/syntask/startSynch', data);
}

//获取syntaskId分组列表
export function listAllSyntaskGroup(data) {
    return requestV1.get(prefix+'/syntask/listAllSyntaskGroup', data);
}


//编辑标签分组
export function editGroup(data) {
     return requestV1.putJson(prefix+'/syntask/editGroup', data);
}

//删除标签分组
export function deleteGroup(data) {
     return requestV1.deleteForm(prefix+'/syntask/deleteGroup', data);
}

//新增标签分组
export function addGroup(data) {
     return requestV1.postJson(prefix+'/syntask/addGroup', data);
}

//编辑标签同步任务
export function edit(data) {
   return requestV1.putJson(prefix+'/syntask/edit', data);
}

//根据ID获取标签同步任务详情
export function findById(data) {
    return requestV1.get(prefix+'/syntask/findById', data);
}

//获取公众号列表
export function listWxAuthByType(data) {
     return requestV1.get(prefix+'/syntask/listWxAuthByType', data);
}

//新增标签同步任务
export function add(data) {
     return requestV1.postJson(prefix+'/syntask/add', data);
}

//删除标签同步任务
export function deletes(data) {
     return requestV1.postJson(prefix+'/syntask/deletes', data);
}

//查询表格分页数据
export function queryPage(data) {
    return requestV1.postJson(prefix+'/syntask/queryPage', data);
}
