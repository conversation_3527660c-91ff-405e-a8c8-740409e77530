<template>
  <div class="dynamics-form-page-v6">
    <template v-for="page in pageContent">
      <div
        class="dynamics-form-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="dynamics-form-top"></div>
        <template v-if="page.once">
          <div class="dynamics-form-t">{{ subTitle }}</div>
          <div class="dynamics-form-ct">
            <div class="dynamics-form-ctl"></div>
            调研内容
          </div>
        </template>
        <div class="dynamics-table" :id="page.uuid">
          <template v-for="(topic, topicIdx) in page.children">
            <!-- 单选 -->
            <div
              class="page-content-radio"
              :key="topic.uuid"
              v-if="topic.type === 1"
            >
              <div class="page-content-label">
                {{ topicIdx + 1 + page.startCount }}、{{ topic.title }}
              </div>
              <div class="page-content-value" :class="{
                twoClass: topic.formTemplateOptions.length === 2
              }">
                <el-radio-group
                  :size="comSize"
                  v-model="radioVal"
                  v-if="topic.formTemplateOptions.length > 0"
                  class="defaultClass"
                >
                  <div
                    class="option-item"
                    v-for="item in topic.formTemplateOptions"
                    :key="item.value"
                  >
                    <el-radio :label="item.value" :disabled="disabled">{{
                      item.label
                    }}</el-radio>
                  </div>
                </el-radio-group>
              </div>
            </div>
            <!-- 多选 -->
            <div
              class="page-content-radio"
              :key="topic.uuid"
              v-else-if="topic.type === 2"
            >
              <div class="page-content-label">
                {{ topicIdx + 1 + page.startCount }}、{{ topic.title }}
              </div>
              <div class="page-content-value">
                <el-checkbox-group
                  :size="comSize"
                  v-model="checkboxVal"
                  v-if="topic.formTemplateOptions.length > 0"
                  class="defaultClass"
                >
                  <div
                    class="option-item"
                    v-for="item in topic.formTemplateOptions"
                    :key="item.value"
                  >
                    <el-checkbox
                      :label="item.value"
                      :disabled="disabled"
                    ></el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
            <div class="page-content-input" :key="topic.uuid" v-else>
              <div class="page-content-label">
                {{ topicIdx + 1 + page.startCount }}、{{ topic.title }}
              </div>
              <div class="page-content-value">
                {{ topic.placeholder || "" }}
              </div>
            </div>
          </template>
        </div>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      subTitle: '',
      disabled: false,
      checkboxVal: [],
      radioVal: null,
      startCount: 0,
      uuidKey: "dynamics-form-page",
      comSize: "mini",
      filePrex:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/",
      pageContent: [],
    };
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let answerReportVoList = pageObject.answerReportVoList || [];
      this.subTitle = pageObject.subTitle;
      for (let i = 0; i < answerReportVoList.length; i++) {
        let item = answerReportVoList[i];
        await this.rendCurrent(item, {
          type: "dynamics-form-page",
          once: this.pageContent.length === 0,
          children: [],
          abcCount: 2,
          startCount: this.startCount || 0,
        });
        this.startCount += 1;
      }
      await this.$nextTick();
      this.trimSuccess();
      console.log("answerReportVoList", answerReportVoList, this.pageContent);
    },
  },
  watch: {
    updatecount(n) {
      this.pageHeight = this.pageSize.height - 63;
      this.initMethod();
    },
  },
};
</script>

<style lang="scss" scoped>
$borderColor: #dcdfe6;
$textColor: #abb2c0;
$mainColor: #609bd3;
.dynamics-form-page-v6 {
  background: #fff;

  .dynamics-form-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 111px 48px 20px;
  }
  .dynamics-form-top {
    height: 79px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 5% 0%;
  }
  .page-content-radio {
    margin-bottom: 45px;
    .page-content-label {
      height: 27px;
      font-weight: 500;
      font-size: 19px;
      color: #333333;
      margin-bottom: 13px;
    }
    .page-content-value {
      color: $textColor;
      border-radius: 5px;
      overflow: hidden;
    }
    
  }
  .page-content-input {
    margin-bottom: 45px;
    .page-content-label {
      height: 27px;
      font-weight: 500;
      font-size: 19px;
      color: #333333;
      margin-bottom: 13px;
    }
    .page-content-value {
      color: $textColor;
      border: 1px solid $borderColor;
      height: 39px;
      border-radius: 5px;
      overflow: hidden;
      display: flex;
      align-items: center;
      padding-left: 13px;
    }
  }
  .option-item {
    display: inline-block;
    min-width: 30%;
    margin-right: 20px;
    margin-bottom: 16px;
  }
  .dynamics-form-t {
    // margin-top: 43px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 33px;
    background-size: 100% 100%;
  }
  .dynamics-form-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
  }
  .dynamics-form-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .page-content {
  }
  .dynamics-table {
    margin-top: 39px;
  }
  .defaultClass {
    width: 100%;
  }
}
</style>