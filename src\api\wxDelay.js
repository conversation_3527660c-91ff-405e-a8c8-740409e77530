import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//根据id获取延迟推送详情
export function getWxDelayById(data) {
    return requestV1.get(prefix+'/wxDelay/getWxDelayById', data);
}

//修改状态
export function updateLaunchStatus(data) {
   return requestV1.putJson(prefix+'/wxDelay/updateLaunchStatus', data);
}

//批量解绑与广告主关联
export function batchRemoveAuth(data) {
   return requestV1.postJson(prefix+'/wxDelay/batchRemoveAuth', data);
}

//批量与广告主关联
export function batchBindAuth(data) {
     return requestV1.postJson(prefix+'/wxDelay/batchBindAuth', data);
}

//分页查询配置已关联和未关联的广告主
export function queryAuthPage(data) {
    return requestV1.postJson(prefix+'/wxDelay/queryAuthPage', data);
}

//删除延迟推送
export function deleteWxDelay(data) {
     return requestV1.deleteForm(prefix+'/wxDelay/deleteWxDelay', data);
}

//修改延迟推送
export function updateWxDelay(data) {
      return requestV1.putJson(prefix+'/wxDelay/updateWxDelay', data);
}

//删除延迟推送消息
export function deleteWxDelayPoster(data) {
    return requestV1.deleteForm(prefix+'/wxDelay/deleteWxDelayPoster', data);
}

//修改延迟推送消息
export function updateWxDelayPoster(data) {
   return requestV1.putJson(prefix+'/wxDelay/updateWxDelayPoster', data);
}

//新增延迟推送消息
export function addWxDelayPoster(data) {
     return requestV1.postJson(prefix+'/wxDelay/addWxDelayPoster', data);
}

//根据延迟推送id查询消息列表
export function getWxDelayPosterList(data) {
      return requestV1.get(prefix+'/wxDelay/getWxDelayPosterList', data);
}

//新增延迟推送
export function addWxDelay(data) {
     return requestV1.postJson(prefix+'/wxDelay/addWxDelay', data);
}

//查询延迟推送分页数据
export function queryDelayPage(data) {
     return requestV1.postJson(prefix+'/wxDelay/queryDelayPage', data);
}
