/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'


let prefix = '/manage/api'

//查询所有广告主列表
export function queryAllWxAuthList(data) {
    return requestV1.get(prefix + '/wxAuth/queryAllWxAuthList', data);
}

//批量设置广告主欢迎语
export function batchSetWxAuthPoster(data) {
    return requestV1.postJson(prefix + '/wxAuth/batchSetWxAuthPoster', data);
}

//批量删除广告主所有欢迎语推送文案
export function deleteByAuthIds(data) {
    return requestV1.deleteForm(prefix + '/wxAuth/deleteByAuthIds', data);
}

//公众号出售记录编辑
export function authSellLogEdit(data) {
    return requestV1.postJson(prefix + '/wxAuth/authSellLogEdit', data);
}

//公众号出售记录根据广告主id获取
export function getWxAuthSellLogById(data) {
    return requestV1.get(prefix + '/wxAuth/getWxAuthSellLogById', data);
}

//公众号自营情况编辑
export function operationLogEdit(data) {
    return requestV1.postJson(prefix + '/wxAuth/operationLogEdit', data);
}

//公众号自营情况分页
export function operationLogqueryPage(data) {
    return requestV1.postJson(prefix + '/wxAuth/operationLogqueryPage', data);
}


//根据授权类型获取正常状态广告主列表
export function listNormalWxAuthByType(data) {
    return requestV1.postJson(prefix + '/wxAuth/listNormalWxAuthByType', data);
}

//新增广告主
export function updateTagConfig(data) {
    return requestV1.putJson(prefix + '/wxAuth/updateTagConfig', data);
}

//新增广告主
export function add(data) {
    return requestV1.postJson(prefix + '/wxAuth/add', data);
}

//编辑广告主
export function edit(data) {
    return requestV1.putJson(prefix + '/wxAuth/edit', data);
}

//修改/设置广告主所属运营商
export function changeAdvertiseId(data) {
    return requestV1.postJson(prefix + '/wxAuth/changeAdvertiseId', data);
}

//所有广告主
export function queryList(data) {
    return requestV1.postJson(prefix + '/wxAuth/queryList', data);
}

//注销广告主
export function deleteWxAuth(data) {
    return requestV1.deleteJson(prefix + '/wxAuth/delete', data);
}

//查询已认证广告主列表
export function queryVerifiedWxAuthList(data) {
    return requestV1.get(prefix + '/wxAuth/queryVerifiedWxAuthList', data);
}

// 获取个微关注回调地址
export function getPersonalSubscribeCallbackUrl (data) {
    return requestV1.get(`${prefix}/wxAuth/getPersonalSubscribeCallbackUrl`, data)
}


// 微信落地页配置批量修改 /api/landingPageConfig/batch/switch
export function landingPageConfigbatchswitch (data = {}) {
    return requestV1.putJson(`${prefix}/landingPageConfig/batch/switch`, data)
}


// 引流号成员管理 api/wxcontactUser/queryPersonalMemberPage
export function wxcontactUserqueryPersonalMemberPage (data = {}) {
    return requestV1.postJson(`${prefix}/wxcontactUser/queryPersonalMemberPage`, data)
}