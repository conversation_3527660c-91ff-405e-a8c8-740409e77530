import requestV1 from '@/common/utils/modules/request'

let prefix = '/sop/api'
    //添加评论
export function addComment(data) {
    return requestV1.postJson(prefix + '/workOrder/addComment', data);
}

//完成工单
export function finishWorkOrder(data) {
    return requestV1.postJson(prefix + '/workOrder/finishWorkOrder', data);
}

//根据工单ID获取详情
export function findById(data) {
    return requestV1.get(prefix + '/workOrder/findById', data);
}

//工单接单
export function processedWorkOrder(data) {
    return requestV1.putForm(prefix + '/workOrder/processedWorkOrder', data);
}

//编辑工单
export function edit(data) {
    return requestV1.putJson(prefix + '/workOrder/edit', data);
}

//新增工单
export function add(data) {
    return requestV1.postJson(prefix + '/workOrder/add', data);
}

//导出数据
export function exportData(data) {
    return '/export/api/workOrder/exportData'
}

//根据安装单位获取机构备案记录列表
export function queryPage(data) {
    return requestV1.postJson(prefix + '/workOrder/queryPage', data);
}