import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'

/**
 * 执行任务管理
 */

// 上传结案
export function completedFinalReport(data) {
  return requestV1.putJson(`${prefix}/project/completedFinalReport`, data)
}

// 根据ids批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/todotasks/delete/batch/${data.ids}`);
}

// 根据id指定删除
export function deleteOne(data) {
  return requestV1.deleteForm(`${prefix}/todotasks/delete/batch/${data.id}`);
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/todotasks/insert`, data)
}

// 列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/todotasks/query/list`, data)
}

// 根据id查询单个
export function queryOne(data,expandHeaders = {}) {
  return requestV1.get(`${prefix}/todotasks/query/one`, data, null, expandHeaders)
}

// 分页列表查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/todotasks/query/page`, data)
}

// 根据多参数进行单一查询
export function queryParam(data) {
  return requestV1.get(`${prefix}/todotasks/query/param`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/todotasks/update`, data)
}

// 关闭
export function closeTasks(data) {
  return requestV1.putForm(`${prefix}/todotasks/closeTasks`, data)
}

// 用任务子项关联的签到的IP来请求获取
export function getCommonippoolParseIp(data) {
  return requestV1.get(`${prefix}/commonippool/parse/ip`, data)
}

// 发送任务短信
export function sendTaskSms(data) {
  return requestV1.postForm(`${prefix}/todotasks/send/task/sms`, data)
}

// 批量发送任务短信
export function batchSendTaskSms(data) {
  return requestV1.postForm(`${prefix}/todotasks/batch/send/task/sms`, data)
}

// 批量导入任务
export const importExcel = env.ctx + prefix + '/todotasks/import/excel'

// 克隆签到
export function signClone(data) {
  return requestV1.postJson(`${prefix}/todotasks/sign/clone`, data)
}


// 问卷推广码 
// 
export function todotasksitemgeneratepromotionCode(data) {
  return requestV1.postJson(`${prefix}/todotasksitem/generate/promotionCode`, data)
}

// 签到修复
export function signFix(data) {
  return requestV1.postJson(`${prefix}/todotasks/sign/fix`, data)
}

// 导入更新任务进度
export const importScheduleExcel = env.ctx + prefix + '/todotasksitem/import/excel'

// 批量生成运营计划
export function generateChannelTaskBatch(data) {
  return requestV1.postJson(`${prefix}/todotasks/generate/channelTask/batch`, data)
}

// 批量撤销
export function cancelBatch(data) {
  return requestV1.putJson(`${prefix}/todotasks/cancel/batch`, data)
}

// 批量修改金额
export function batchUpdateIncentiveFeePrice(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/update/incentive/fee/price`, data)
}

// 批量修正任务绩效
export function batchTodotasksUpdatePerformance(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/update/performance`, data)
}

// 批量设置单价
export function batchTodotasksUpdatePartData(data) {
  return requestV1.putJson(`${prefix}/todotasks/update/part/data`, data)
}

// 获取任务个人精准地推报告 
export function todotasksGetReportPersonal(data,expandHeaders = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/personal`, data, null, expandHeaders)
}

// 批量生成渠道链任务 
export function todotasksChannelLinkGenerate(data) {
  return requestV1.get(`${prefix}/todotasks/channelLink/generate`, data)
}

// 任务列表克隆签到操作
export function todotasksCloneSignFromOtherTenant(data) {
  return requestV1.get(`${prefix}/todotasks/clone/sign/from/otherTenant`, data)
}

// 任务列表克隆签到操作
export function todotasksSignFixV2(data) {
  return requestV1.postJson(`${prefix}/todotasks/clone/sign/from/otherTenant/v2`, data)
}

// 从其他任务中克隆签到记录的图片到本任务子项
export function todotasksClonePic(data) {
  return requestV1.get(`${prefix}/todotasks/clone/pic`, data)
}

// 任务列表-添加批量修改任务状态
export function todotasksBatchUpdateTaskStatus(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/update/taskStatus`, data)
}

// 发送驳回短信
export function todotasksSendPicIllegalSms(data) {
  return requestV1.postForm(`${prefix}/todotasks/send/picIllegalSms`, data)
}

// 查询未绑定关联活动（businessId）的任务列表
export function getListNotBindTask(data) {
  return requestV1.get(`${prefix}/todotasks/get/list/not/bind/task`, data)
}

// 更新用户手机号 
export function todotasksUpdateReceiveUserIds(data) {
  return requestV1.postJson(`${prefix}/todotasks/update/receiveUserIds`, data)
}

// 批量审核任务
export function todotasksBatchAudit(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/audit`, data)
}

// 陪诊获取数据 
export function todotasksGetTodoTaskReport(data) {
  return requestV1.postJson(`${prefix}/todotasks/get/todoTask/report`, data)
}

// 克隆任务到租户 
export function cloneTaskToTargetTenant(data) {
  return requestV1.postJson(`${prefix}/todotasks/clone/task/to/target/tenant`, data)
}

// 批量更新用工平台对接配置
export function batchUpdateThirdDataJson(data) {
  return requestV1.postJson(`${prefix}/todotasks/batch/update/thirdDataJson`, data)
}