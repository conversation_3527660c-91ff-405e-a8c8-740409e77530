/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/sop/api'
//查询list分页数据
export function queryList(data) {
  return requestV1.postJson(prefix+'/promoteCustomer/queryList', data);
}

//根据ID获取推广客户信息
export function findById(data) {
  return requestV1.get(prefix+'/promoteCustomer/findById', data);
}

//编辑推广客户
export function edit(data) {
  return requestV1.putJson(prefix+'/promoteCustomer/edit', data);
}

//新增推广客户
export function add(data) {
  return requestV1.postJson(prefix+'/promoteCustomer/add', data);
}

//分页查询
export function queryPage(data) {
  return requestV1.postJson(prefix+'/promoteCustomer/queryPage', data);
}
