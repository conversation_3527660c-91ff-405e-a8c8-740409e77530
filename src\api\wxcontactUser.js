import requestV1 from '@/common/utils/modules/request'
let prefix = '/manage/api'
//获取企业微信成员二维码
export function getWxCpMemberQrCode(data) {
    return requestV1.get(prefix + '/wxcontactUser/getWxCpMemberQrCode', data);
}

//查询已配置客户联系功能的企业微信用户信息
export function findById(data) {
    return requestV1.get(prefix + '/wxcontactUser/findById', data);
}

//根据授权authId查询已配置客户联系功能的企业微信用户列表
export function findListByAuthId(data) {
    return requestV1.get(prefix + '/wxAuthGroup/findListByAuthId', data);
}


// 根据引流号ID获取个微成员列表
export function getPersonalList(data) {
    return requestV1.get(prefix + '/wxcontactUser/getPersonalList', data);
}

// 添加个微成员信息
export function savePersonal(data) {
    return requestV1.postJson(prefix + '/wxcontactUser/savePersonal', data);
}

// 编辑个微成员信息
export function editPersonal(data) {
    return requestV1.putJson(prefix + '/wxcontactUser/editPersonal', data);
}

// 查询个微成员分页
export function queryPersonalMemberPage(data) {
    return requestV1.postJson(prefix + '/wxcontactUser/queryPersonalMemberPage', data)
}

