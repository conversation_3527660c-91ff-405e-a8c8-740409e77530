/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'
import env from "@/config/env";

let prefix = '/manage/api/v1'

// excel导入数据
const uploadExcel = env.ctx + '/export/api/v1/separateaccountconfig/uploadexcel'
export { uploadExcel }

//根据经销商id获取集合
export function getLIstByOperatorid(data) {
    return requestV1.get(prefix + '/separateaccountconfig/get/list/by/operatorid', data);
}

//删除
export function deleteOne(data) {
    return requestV1.deleteForm(`${prefix}/separateaccountconfig/delete/one/${data}`);
}

//新增
export function insert(data) {
    return requestV1.postJson(prefix + '/separateaccountconfig/insert', data);
}

//根据多参数进行列表查询
export function queryList(data) {
    return requestV1.get(prefix + '/separateaccountconfig/query/list', data);
}

//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/separateaccountconfig/query/one', data);
}

//分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/separateaccountconfig/query/page', data);
}

//更新
export function update(data) {
    return requestV1.putJson(prefix + '/separateaccountconfig/update', data);
}

// 根据分账id获取经销商集合
export function getChannelOperatorByConfigid(data) {
  return requestV1.get(prefix + '/separateaccountconfig/get/channel/operator/by/configid', data);
}
//批量设置分账属性
export function separateAccount(data) {
    return requestV1.postForm(prefix + '/separateaccountconfig/batch/bind/auth/separate/account', data);
}
