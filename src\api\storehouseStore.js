/* eslint-disable */

import requestV1 from '@/common/utils/modules/request'

import env from '@/config/env'

let prefix = '/sop/api'

const uploadExcelRecord = env.ctx + prefix + '/storehouseStore/importShareOutStockToCorrect'

export { uploadExcelRecord }



//编辑共享仓出库
export function editOutShareStore(data) {
  return requestV1.putJson(prefix + '/storehouseStore/editOutShareStore', data);
}

//编辑共享仓入库
export function editInShareStore(data) {
  return requestV1.putJson(prefix + '/storehouseStore/editInShareStore', data);
}

//获取仓库存汇总统计(入库、出库、剩余)
export function getStoreOutInStatistics(data) {
  return requestV1.postJson(prefix + '/storehouseStore/getStoreOutInStatistics', data);
}

//编辑共享仓换货记录
export function editReplaceShareStore(data) {
  return requestV1.putJson(prefix + '/storehouseStore/editReplaceShareStore', data);
}

//新增共享仓换货
export function addReplaceShareStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addReplaceShareStore', data);
}

//编辑出入库记录
export function editOutInStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/editOutInStore', data);
}

export function editInSelfStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/editInSelfStore', data);
}

//注销出入库记录
export function deleteStoreRecords(data) {
  return requestV1.postJson(prefix + '/storehouseStore/deleteStoreRecords', data);
}

//新增共享仓入库
export function addInShareStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addInShareStore', data);
}

//新增共享仓出库
export function addOutShareStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addOutShareStore', data);
}


//新增仓库库存
export function addInCustomerStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addInCustomerStore', data);
}
//查询共享仓库库存分页数据
export function queryShareStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStore/queryShareStorePage', data);
}

//查询客户仓库库存分页数据
export function queryCustomerStorePage(data) {
  return requestV1.postJson(prefix + '/storehouseStore/queryCustomerStorePage', data);
}

//新增自营仓入库
export function addInSelfStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addInSelfStore', data);
}

export function addOutSelfStore(data) {
  return requestV1.postJson(prefix + '/storehouseStore/addOutSelfStore', data);
}

//查询仓库库存列表
export function queryStoreList(data) {
  return requestV1.postJson(prefix + '/storehouseStore/queryStoreList', data);
}

export function editOutSelfStore(data) {
  return requestV1.putJson(prefix + '/storehouseStore/editOutSelfStore', data);
}

// 获取共享仓库存
export function getShareStorehouseStore(data) {
  return requestV1.get(prefix + '/storehouseStore/getShareStorehouseStore', data)
}

// 获取库存根据客户和库存类型
export function getStoreByCustomerIdAndStorehouseType(data) {
  return requestV1.get(`${prefix}/storehouseStore/getStoreByCustomerIdAndStorehouseType`, data)
}

// 更新客户共享仓库存
export function updateAllShareStore(data) {
  return requestV1.postForm(`${prefix}/storehouseStore/updateAllShareStore`, data)
}

// 更新自营仓库存
export function updateAllSelfStore(data) {
  return requestV1.postJson(`${prefix}/storehouseStore/updateAllSelfStore`, data)
}

// 更新袋子仓所有库存
export function updateAllCustomerStore(data) {
  return requestV1.postForm(`${prefix}/storehouseStore/updateAllCustomerStore`, data)
}

//
export function getStorehouseInfoList(data) {
  return requestV1.get(`${prefix}/storehouseStore/get/storehouse/info/list`, data)
}

export function storehouseStoreShareStoreImport(data) {
  return requestV1.postJson(`${prefix}/storehouseStore/shareStore/import`, data)
}

// 库存调整
export function storehouseStoreStockReset(data) {
  return requestV1.postJson(`${prefix}/storehouseStore/stock/reset`, data)
}

// 统计 
export function storehouseStoreRecordSelfStatistics(data) {
  return requestV1.postForm(`${prefix}/storehouseStoreRecord/self/store/statistics`, data)
}

// 自营出库导入
export const importStorehouseStoreOutSelf = `${env.ctx}${prefix}/storehouseStore/out/self/store/import`

// 定制方列表
export function customerCustomPersonQueryList(data) {
  return requestV1.postJson(`${prefix}/customer/customPerson/queryList`, data)
}

// 根据自营仓id 物料id获取自营仓库存
export function getStorehouseByMaterialIdSelfHouse(data) {
  return requestV1.get(`${prefix}/storehouseStore/get/storehouse/by/materialId/selfHouse`, data)
}
