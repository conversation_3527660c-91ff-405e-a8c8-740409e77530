import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api/v1'
    //分页列表查询
export function queryPage(data) {
    return requestV1.postJson(prefix + '/withdrawalapply/query/page', data);
}

//更新提现状态
export function update(data) {
    return requestV1.putJson(prefix + '/withdrawalapply/update', data);
}
//根据主键单一查询
export function queryOne(data) {
    return requestV1.get(prefix + '/withdrawalapply/query/one', data);
}

//保存数据
export function insert(data) {
    return requestV1.postJson(prefix + '/withdrawalapply/insert', data);
}