/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
    //修改提现金额
export function modifyWithdrawAmount(data) {
    return requestV1.postJson(prefix + '/operatorWithdraw/modifyWithdrawAmount', data);
}
//自动对账财务明细科目汇总统计
export function queryReconciliationDetail(data) {
    return requestV1.get(prefix + '/operatorWithdraw/queryReconciliationDetail', data);
}

//根据ID获取提现申请详情
export function findById(data) {
    return requestV1.postForm(prefix + '/operatorWithdraw/findById', data);
}

//保存备注
export function saveReconciliationRemark(data) {
    return requestV1.postForm(prefix + '/operatorWithdraw/saveReconciliationRemark', data);
}