/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//编辑自动回复消息配置
export function edit(data) {
  return requestV1.putJson(prefix+'/msgReply/edit', data);
}

//注销自动回复消息配置
export function deletes(data) {
  return requestV1.postJson(prefix+'/msgReply/deletes', data);
}

//新增自动回复消息配置
export function add(data) {
  return requestV1.postJson(prefix+'/msgReply/add', data);
}

//查询自动回复消息配置分页数据
export function queryPage(data) {
  return requestV1.postJson(prefix+'/msgReply/queryPage', data);
}
