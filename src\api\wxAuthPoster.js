import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//新增或编辑欢迎语
export function save(data) {
    return requestV1.postJson(prefix + '/wxAuthPoster/save', data);
}

//删除欢迎语
export function deletes(data) {
    return requestV1.deleteForm(prefix + '/wxAuthPoster/delete', data);
}

//获取欢迎语列表
export function getPosterListByRelId(data) {
    return requestV1.get(prefix + '/wxAuthPoster/getPosterListByRelId', data);
}

//根据事件类型获取租户欢迎语
export function getWxAuthPosterByEventType(data) {
    return requestV1.get(prefix + '/wxAuthPoster/getWxAuthPosterByEventType', data);
}

//添加小程序出袋欢迎语
export function addMiniAppPoster(data) {
    return requestV1.postJson(prefix + '/wxAuthPoster/addMiniAppPoster', data);
}