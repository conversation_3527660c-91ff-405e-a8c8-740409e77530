import requestV1 from '@/common/utils/modules/request'
const prefix = '/manage/api/v1'

// 批量与安装单位关联
export function batchBindUnit (data) {
    return requestV1.postJson('/manage/api/batchBindAuth', data)
}

// 批量解绑与安装单位关联
export function batchRemoveUnit (data) {
    return requestV1.postJson('/manage/api/batchRemoveAuth', data)
}

// 查询关联广告主分页数据
export function queryAuthPage (data) {
    return requestV1.postJson('/manage/api/queryAuthPage', data)
}

// 批量删除数据
export function batchDelete (data) {
    return requestV1.deleteForm(`${prefix}/welcometemplate/delete/batch`, data)
}

// 保存欢迎语模板数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/welcometemplate/insert`, data)
}

// 欢迎语模板列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/welcometemplate/query/list`,data)
}

// 根据ID获取欢迎语模板
export function queryOne (data) {
    return requestV1.get(`${prefix}/welcometemplate/query/one`, data)
}

// 欢迎语模板分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/welcometemplate/query/page`, data)
}

// 更新欢迎语模板数据
export function update (data) {
    return requestV1.putJson(`${prefix}/welcometemplate/update`, data)
}
