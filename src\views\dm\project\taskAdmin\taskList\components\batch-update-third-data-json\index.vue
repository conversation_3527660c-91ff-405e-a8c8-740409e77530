<template>
  <el-dialog
    :title="`批量更新支付编码`"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    center
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '170px' }"
    >
      <div slot="btnList">
        <el-button
          type="primary"
          size="mini"
          @click="confirm"
          :loading="confirmLoading"
          >确定</el-button
        >
        <el-button type="danger" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
  </el-dialog>
</template>

<script>
import formMixin from "@/mixin/formMixin";
import { batchUpdateThirdDataJson } from '@/api/todotasks.js'
import { getFromData } from '@/utils/index'
const defaultFormList = [
  {
    title: "支付编码",
    id: "thirdData",
    value: null,
    type: 9,
    option: [],
    must: true
  }
]

export default {
  mixins: [formMixin],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    paramsData: {
      type: Array,
      default: function () {
        return [];
      },
    }
  },
  data() {
    return {
      formList: JSON.parse(JSON.stringify(defaultFormList)),
      confirmLoading: false,
      dialogVisible: false,
    };
  },
  methods: {
    async confirm() {
      let formParam = getFromData(this.formList);
      if (!formParam) return;
      let res = null;
      formParam = {
        ...formParam,
        taskIds: Array.isArray(this.paramsData) ? this.paramsData.map(item => item.id) : this.paramsData.id,
      };
      this.confirmLoading = true
      res = await batchUpdateThirdDataJson(formParam).catch(() => { this.confirmLoading = false })
      this.confirmLoading = false
      this.$eltool.successMsg(res.msg);
      this.close('query')
    },
    clear() {
      this.formList.forEach(item => {
        item.value = null;
      })
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit('update:show', false)
      this.$emit("close", type);
    },
  },
  watch: {
    show(n) {
      this.dialogVisible = n;
      if (!n) {
        this.formList = JSON.parse(JSON.stringify(defaultFormList))
        return
      }
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .search .input {
  width: 100%;
}
</style>