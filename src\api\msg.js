/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//获取公告消息详情
export function findAnnouncementMsgById(data) {
  return requestV1.get('/manage/api/msg/findAnnouncementMsgById', data);
}

//编辑公告消息
export function editAnnouncementMsg(data) {
  return requestV1.putJson(prefix+'/msg/editAnnouncementMsg', data);
}

//添加公告消息
export function addAnnouncementMsg(data) {
  return requestV1.postJson(prefix+'/msg/addAnnouncementMsg', data);
}

//启动推送公告消息
export function pushAnnouncementMsg(data) {
  return requestV1.postForm(prefix+'/msg/pushAnnouncementMsg', data);
}

//查询公告消息分页数据
export function queryAnnouncementMsgPage(data) {
  return requestV1.postJson(prefix+'/msg/queryAnnouncementMsgPage', data);
}

//查询消息表格分页数据
export function queryUserMsgPage(data) {
  return requestV1.postJson(prefix+'/msg/queryUserMsgPage', data);
}

//查询表格分页数据
export function queryUserBingMsgPage(data) {
  return requestV1.postJson(prefix+'/msg/queryUserBingMsgPage', data);
}

//批量用户绑定模板消息
export function batchAddUserMsg(data) {
  return requestV1.postJson(prefix+'/msg/batchAddUserMsg', data);
}

//获取所有消息模板类型
export function listAllMsgType(data) {
  return requestV1.get(prefix+'/msg/listAllMsgType', data);
}
