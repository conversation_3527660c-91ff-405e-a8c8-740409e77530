<template>
  <div
    class="topic-rect-page-v4-v6"
    :class="{
      oneClass: pagetype === 4,
    }"
  >
    <template v-for="page in pageContent">
      <div
        class="topic-rect-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="topic-rect-top"></div>
        <template v-if="page.once">
          <div class="topic-rect-ct">
            <div class="topic-rect-ctl"></div>
            {{ subTitle }}
          </div>
        </template>
        <template v-if="isEchartOpen">
          <div class="topic-echart">
            <div class="echart-item" :id="page.echatUuid"></div>
          </div>
        </template>
        <div class="pull-table-box table-size14" :id="page.uuid">
          <div class="pull-table">
            <el-table
              :data="formTemplateOptions.slice(page.start, page.end)"
              header-row-class-name="pull-new-details-table-row"
              cell-class-name="pull-new-details-table-cell"
              style="width: 100%"
              v-if="formTemplateOptions.length > 0"
            >
              <template v-for="col in tableHeader">
                <el-table-column
                  :prop="col.prop"
                  :label="col.label"
                  :width="col.width"
                  align="center"
                  :key="col.prop"
                  :isReportTable="true"
                >
                </el-table-column>
              </template>
            </el-table>
          </div>
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";

export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "topic-rect-page",
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
    isEchartOpen: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      pageHeight: this.pageSize.height - 53,
      boxWidth: this.pageSize.width - 134,
      tableWidth: this.pageSize.width - 134,
      borderWidth: null,
      widthZoom: 1,
      subTitle: "",
      pageContent: [
        {
          type: "topic-rect-page",
          start: 0,
          end: 6,
          once: true,
          headers: [],
          widthZoom: 1,
          borderWidth: null,
          pageTitle: "",
          children: [],
        },
      ],
      // tableHeader: [],
      spaceImageCount: 6,
      formTemplateOptions: [],
    };
  },
  watch: {
    updatecount(n) {
      if (this.pagetype === 4) {
        this.boxWidth = this.pageSize.width - 184;
        this.tableWidth = this.pageSize.width - 184;
      }
      this.initMethod();
    },
    renderUpdateCount(n) {
      if (this.isEchartOpen) {
        this.initRenderChart();
      }
    },
  },
  methods: {
    async initRenderChart() {
      let pageObject = this.pageObject || {};
      // let topicResult = pageObject.topicResult;
      for (let i = 0; i < this.pageContent.length; i++) {
        let item = this.pageContent[i];
        let seriesDataOne = this.formTemplateOptions.slice(
          item.start,
          item.end
        );
        let chartOne = {
          belongType: 30,
          seriesData: seriesDataOne,
        };
        let chartUuidOne = "#" + item.echatUuid;
        if (seriesDataOne.length > 0) {
          let xAxisOption = {};
          let yAxisOption = {};
          if (this.inType === "number-of-visits-to-pharmacies") {
            xAxisOption = {
              name: "执行人",
              axisLabel: {
                rotate: 45,
                interval: 0,
              },
            };
            yAxisOption = {
              name: "完成药店拜访数量",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            };
          } else if (this.inType === "number-of-visits-to-questionnaire") {
            xAxisOption = {
              name: "执行人",
              axisLabel: {
                rotate: 45,
                interval: 0,
              },
            };
            yAxisOption = {
              name: "完成问卷数量",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            };
          }
          this.initBarChart2D(chartOne, chartUuidOne, {
            color: "#9dd0ff",
            xAxisOption: xAxisOption,
            yAxisOption: yAxisOption,
          });
        }
      }
    },
    async initRender() {
      let pageObject = this.pageObject || {};
      let item = pageObject.answerReportVo || [];
      if (!item) {
        this.trimSuccess();
      }
      let formTemplateOptions = item.formTemplateOptions || [];
      this.formTemplateOptions = formTemplateOptions.map((item) => {
        return {
          ...item,
          name: item.optionValue,
          value: item.selectOptionNum,
          percent: item.selectOptionProportion,
        };
      });
      this.sceentInit(
        {
          type: "topic-rect-page",
          once: false,
          headers: [],
          widthZoom: 1,
          borderWidth: null,
          children: [],
        },
        formTemplateOptions
      );
      await this.$nextTick();
      this.trimSuccess();
    },
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      this.pageHeight = this.pageSize.height - 63;
      this.uuidKey = this.inType;
      if (this.pageContent.length > 0) {
        this.pageContent[0].uuid = this.getUuid();
        this.pageContent[0].echatUuid = this.getUuid();
        if (!this.isEchartOpen) {
          this.pageContent[0].end = 16;
          this.spaceImageCount = 16;
        }
      }
      this.initRender();
    },
  },
  computed: {
    tableHeader(n) {
      if (this.inType === "number-of-visits-to-pharmacies") {
        n = [
          {
            prop: "optionValue",
            label: "执行人姓名",
            width: 144,
          },
          {
            prop: "userPhone",
            label: "手机号",
            width: 144,
          },
          {
            prop: "genderText",
            label: "性别",
          },
          {
            prop: "selectOptionNum",
            label: "完成药店拜访数量",
            width: 144,
          },
        ];
      } else if (this.inType === "number-of-visits-to-questionnaire") {
        n = [
          {
            prop: "optionValue",
            label: "执行人姓名",
            width: 180,
          },
          {
            prop: "userPhone",
            label: "手机号",
            width: 180,
          },
          {
            prop: "genderText",
            label: "性别",
          },
          {
            prop: "selectOptionNum",
            label: "完成问卷数量",
            width: 180,
          },
        ];
      }
      return n;
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
.topic-rect-page-v4-v6 {
  .topic-rect-page {
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    padding: 119px 67px 20px;
  }
  .topic-rect-top {
    height: 79px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 5% 0%;
  }
  .topic-rect-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-bottom: 33px;
  }
  .topic-rect-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .topic-echart {
    width: 100%;
    height: 416px;
    margin-bottom: 65px;
    // background: yellowgreen;
  }
  .echart-item {
    width: 100%;
    height: 100%;
  }
}
.oneClass {
  .pull-table-box {
    padding: 0 25px;
  }
}
</style>