import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 执行任务管理
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/todotasksitemoperating/delete/batch/${data.ids}`);
}

// 根据id指定删除
export function deleteOne (data) {
    return requestV1.deleteForm(`${prefix}/todotasksitemoperating/delete/batch/${data.id}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/todotasksitemoperating/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/todotasksitemoperating/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/todotasksitemoperating/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/todotasksitemoperating/query/page`, data)
}

// 根据多参数进行单一查询
export function queryParam (data) {
    return requestV1.get(`${prefix}/todotasksitemoperating/query/param`, data)
}

// 子项操作记录
export function queryRecordList (data) {
    return requestV1.get(`${prefix}/todotasksitemoperating/query/record/list`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/todotasksitemoperating/update`, data)
}
