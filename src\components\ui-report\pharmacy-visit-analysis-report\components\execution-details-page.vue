<template>
  <div class="execution-details-page-v6">
    <template v-for="page in pageContent">
      <div
        class="execution-details-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="execution-personnel-data-top"></div>
        <template v-if="page.once">
          <div class="execution-personnel-data-ct">
            <div class="execution-personnel-data-ctl"></div>
            {{ subTitle }}
            <template v-if="totalCount >= 300">
              （仅展示{{ 300 }}条）
            </template>
            <template v-else> （共{{ totalCount }}条） </template>
          </div>
          <div class="space-height"></div>
        </template>
        <div class="pull-table-box table-size24" :id="page.uuid">
          <div
            class="pull-table"
            :style="{
              width: tableWidth + 'px',
              zoom: widthZoom,
              '--borderWidth': borderWidth,
            }"
          >
            <el-table
              :data="page.children"
              header-row-class-name="pull-new-details-table-row"
              cell-class-name="pull-new-details-table-cell"
              style="width: 100%"
            >
              <template v-for="col in tableHeader">
                <el-table-column
                  :prop="col.prop"
                  :label="col.label"
                  :width="col.width"
                  align="center"
                  :key="col.prop"
                  :isReportTable="true"
                >
                  <template slot-scope="scope">
                    {{ scope.row[col.prop] }}
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";
import { queryPage as queryLogApi } from "@/api/dm/visiting/visitingplanobjectlist.js";
export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      totalCount: 0,
      subTitle: "",
      borderWidth: null,
      uuidKey: "execution-personnel-data",
      boxWidth: this.pageSize.width - 80,
      tableWidth: this.pageSize.width - 80,
      filePrex:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/",
      grilIco: "icon-gril.png",
      boyIco: "icon-boy.png",
      pageContent: [
        {
          type: "execution-details-page",
          uuid: "execution-details-page_0",
          children: [],
          once: true,
        },
      ],
      tableHeader: [
        {
          width: 80,
          prop: "cnum",
          label: "编号",
        },
        {
          width: 130,
          prop: "userInfoName",
          label: "拜访人姓名",
        },
        {
          width: 150,
          prop: "userInfoPhone",
          label: "拜访人手机",
        },
        {
          width: 140,
          prop: "drugstoreName",
          label: "药店名称",
        },
        {
          width: 120,
          prop: "drugstoreUserName",
          label: "店员名称",
        },
        {
          width: 160,
          prop: "writeTime",
          label: "拜访日期",
        },
        {
          width: 130,
          prop: "drugstoreDuration",
          label: "拜访时长",
        },
        {
          width: 150,
          prop: "drugstoreTransmit",
          label: "传递关键信息",
        },
      ],
    };
  },
  methods: {
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    headerInit() {
      let sw = this.boxWidth;
      let widthZoom = 1;
      let headersWidth = 0;
      this.tableHeader.forEach((item) => {
        headersWidth += item.width;
      });
      if (headersWidth < sw) {
        headersWidth = sw;
        widthZoom = 1;
        this.tableHeader[this.tableHeader.length - 1].width = null;
      } else {
        headersWidth += this.tableHeader.length * 2;
        this.tableHeader[this.tableHeader.length - 1].width = null;
        widthZoom = (sw / headersWidth).toFixed(3);
      }
      console.log("widthZoom===", widthZoom, sw);
      this.widthZoom = widthZoom;
      this.tableWidth = headersWidth;
      this.borderWidth =
        Math.ceil(this.calculateZoomFactor(widthZoom) * 1) + "px";
    },
    async initMethod() {
      this.headerInit();
      let pageObject = this.pageObject || {};
      let subTitle = pageObject.subTitle;
      this.subTitle = subTitle;
      await this.$nextTick();
      if (!this.pageObject.planId) {
        this.trimSuccess();
      } else {
        this.queryLogApi();
      }
    },
    maskPhone(phone) {
      if (!phone || phone.length < 7) return phone; // 简单校验
      const prefix = phone.slice(0, 3); // 前3位
      const suffix = phone.slice(-4); // 后4位
      return `${prefix}****${suffix}`;
    },
    desensitizeName(vstr = "") {
      if (!vstr) {
        return "";
      }
      vstr += "";
      if (vstr.length === 2) {
        return vstr[0] + "*";
      } else if (vstr.length === 3) {
        return vstr[0] + "**";
      } else {
        let str = vstr[0];
        for (let i = 1; i < vstr.length - 1; i++) {
          str += "*";
        }
        str += "*";
        return str;
      }
    },
    async initRender(list = []) {
      console.log("this.pageHeight", this.pageHeight);
      let cnum = 1;
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        item.cnum = cnum;
        cnum += 1;
        item.userInfoPhone = this.maskPhone(item.userInfoPhone);
        item.userInfoName = this.desensitizeName(item.userInfoName);
        await this.rendCurrent(item, {
          type: "execution-details-page",
          once: this.pageContent.length === 0,
          children: [],
        });
      }
      this.trimSuccess({});
    },
    // 获取执行记录
    async queryLogApi() {
      const res = await queryLogApi({
        condition: {
          planId: this.pageObject.planId,
          type: this.pageObject.planType,
        },
        current: 1,
        size: 300,
      });
      const list = res.data.records;
      this.totalCount = res.data.total;

      await this.initRender(list);
    },
  },
  watch: {
    updatecount(n) {
      this.pageHeight = this.pageSize.height - 63;
      this.initMethod();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
$borderColor: #70a5d7;
.execution-details-page-v6 {
  background: #fff;
  .execution-details-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 111px 40px 20px;
  }
  .execution-personnel-data-top {
    height: 79px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
    background-position: 5% 0%;
  }
  .execution-personnel-data-t {
    margin-top: 43px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 33px;
    background-size: 100% 100%;
  }
  .execution-personnel-data-c {
  }
  .execution-personnel-data-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
  }
  .execution-personnel-data-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .execution-personnel-data-ul {
    display: flex;
    height: 313px;
  }
  .execution-personnel-data-l,
  .execution-personnel-data-r {
    flex: 1;
  }
  .execution-personnel-data-l {
  }
  .execution-personnel-data-li {
    display: flex;
    justify-content: center;
    padding-top: 127px;
    height: 287px;
    box-sizing: border-box;
  }
  .execution-personnel-data-li-i {
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .execution-personnel-data-li-icon-boy {
    width: 53px;
    height: 60px;
    margin-bottom: 20px;
  }
  .execution-personnel-data-li-icon-gril {
    width: 65px;
    height: 59px;
    margin-bottom: 20px;
  }
  .execution-personnel-data-li-tip {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 1.5;
  }
  .execution-personnel-data-r-chart {
    height: 287px;
    width: 100%;
    // background: $mainColor;
  }
  .w26 {
    width: 56px;
    height: 100%;
  }
  .execution-personnel-data-text {
    display: flex;
    justify-content: center;
  }
  .execution-personnel-data-footer {
    border: 1px dashed $borderColor;
    padding: 16px 22px;
    margin-top: 72px;
    line-height: 1.5;
    // font-weight: 500;
    font-size: 19px;
    color: #333333;
  }
  .mgt19 {
    margin-top: 19px;
  }
  .space-height {
    height: 27px;
    width: 100%;
  }
  .mgt33 {
    padding-top: 33px;
  }
}
</style>