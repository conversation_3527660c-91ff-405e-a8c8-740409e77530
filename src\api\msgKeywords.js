/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
//分页获取关键字配置已关联和未关联的消息
export function queryAllMsgReplyByKeywords(data) {
    return requestV1.postJson(prefix+'/msgKeywords/queryAllMsgReplyByKeywords', data);
}

//批量解绑消息与关键字关联
export function batchRemoveMsgToKeywords(data) {
     return requestV1.postJson(prefix+'/msgKeywords/batchRemoveMsgToKeywords', data);
}

//批量新增消息与关键字关联
export function batchAddMsgToKeywords(data) {
   return requestV1.postJson(prefix+'/msgKeywords/batchAddMsgToKeywords', data);
}

//批量解绑广告主与关键字关联
export function batchRemoveAuthToKeywords(data) {
     return requestV1.postJson(prefix+'/msgKeywords/batchRemoveAuthToKeywords', data);
}

//批量新增广告主与关键字关联
export function batchAddAuthToKeywords(data) {
      return requestV1.postJson(prefix+'/msgKeywords/batchAddAuthToKeywords', data);
}

//分页获取关键字配置已关联和未关联的广告主
export function queryAllAuthByKeywords(data) {
   return requestV1.postJson(prefix+'/msgKeywords/queryAllAuthByKeywords', data);
}

//编辑关键字配置
export function edit(data) {
    return requestV1.putJson(prefix+'/msgKeywords/edit', data);
}

//注销关键字配置
export function deletes(data) {
     return requestV1.deleteJson(prefix+'/msgKeywords/deletes', data);
}

//新增关键字配置
export function add(data) {
   return requestV1.postJson(prefix+'/msgKeywords/add', data);
}

//查询关键字配置分页数据
export function queryPage(data) {
   return requestV1.postJson(prefix+'/msgKeywords/queryPage', data);
}
