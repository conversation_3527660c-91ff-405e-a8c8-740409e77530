import requestV1 from '@/common/utils/modules/request'
/* eslint-disable */
import env from '@/config/env'



let prefix = '/sop/api'
// api/storehouseStore/importSelfStore

// excel导入数据
const uploadExcel = env.ctx + prefix + '/storehouseStore/importStockRecord'
const uploadExcelRecord = env.ctx + prefix + '/storehouseStore/importShareStockRecord'
export { uploadExcel, uploadExcelRecord }


//根据客户ID和仓库类型批量新增共享仓或公司仓联系人
export function addContactByType(data) {
  return requestV1.postJson(prefix + '/storehouse/addContactByType', data);
}

//获取仓库联系人列表根据客户id
export function queryStorehouseContactByCustomerId(data) {
  return requestV1.postJson(prefix + '/storehouse/queryStorehouseContactByCustomerId', data);
}

//注销仓库
export function deletes(data) {
  return requestV1.postJson(prefix + '/storehouse/deletes', data);
}

//查询共享袋子仓分页数据
export function queryShareStorehousePage(data) {
  return requestV1.postJson(prefix + '/storehouse/queryShareStorehousePage', data);
}

//编辑客户袋子仓库
export function editCustomerStorehouse(data) {
  return requestV1.putJson(prefix + '/storehouse/editCustomerStorehouse', data);
}

//新增客户袋子仓库
export function addCustomerStorehouse(data) {
  return requestV1.postJson(prefix + '/storehouse/addCustomerStorehouse', data);
}

//根据ID获取仓库详情
export function findById(data) {
  return requestV1.get(prefix + '/storehouse/findById', data);
}

export function addSelfStorehouse(data) {
  return requestV1.postJson(prefix + '/storehouse/addSelfStorehouse', data);
}


export function editSelfStorehouse(data) {
  return requestV1.putJson(prefix + '/storehouse/editSelfStorehouse', data);
}


//获取仓库列表
export function listStorehouse(data) {
  return requestV1.postJson(prefix + '/storehouse/listStorehouse', data);
}

//查询客户袋子仓分页数据
export function queryCustomerStorehousePage(data) {
  return requestV1.postJson(prefix + '/storehouse/queryCustomerStorehousePage', data);
}

export function listContactByStorehouseId(data) {
  return requestV1.get(prefix + '/storehouse/listContactByStorehouseId', data);
}

export function getStorehouseContactByCustomerId(data) {
  return requestV1.get(`${prefix}/storehouse/getStorehouseContactByCustomerId`, data)
}

// 添加仓库联系人
export function addContactByStorehouseId(data) {
  return requestV1.postJson(`${prefix}/storehouse/addContactByStorehouseId`, data)
}

// 供应商列表
export function supplierQueryList(data) {
  return requestV1.get(`${prefix}/v1/supplier/query/list`, data)
}


// 自营仓库库存查询 
export function querySelfStorePage(data) {
  return requestV1.postJson(`${prefix}/storehouseStore/querySelfStorePage`, data)
}

// 获取自营仓库库存列表 
export function querySelfStoreList(data) {
  return requestV1.postJson(`${prefix}/storehouseStore/querySelfStoreList`, data)
}

// 导出明细调整
export function excelExportTaskexcelExportTask(data) {
  return requestV1.postJson(`/export/api/excelExportTask/addExcelTask`, data)
}

// 删除仓库联系人
export function storehouseContactsDeleteBatch(data) {
  return requestV1.deleteJson(`${prefix}/storehouse/storehouseContacts/delete/batch`, data)
}
