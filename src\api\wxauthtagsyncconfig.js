/**
 * 引流号标签同步配置
 */

import request from '@/common/utils/modules/request'

let prefix = '/manage/api/v1'

// 保存数据
export function insert (params) {
    return request.postJson(prefix + '/wxauthtagsyncconfig/insert', params)
}

// 根据多参数进行列表查询
export function queryList (params) {
    return request.get(prefix + '/wxauthtagsyncconfig/query/list', params)
}

// 根据主键单一查询
export function queryOne (params) {
    return request.get(prefix + '/wxauthtagsyncconfig/query/one', params)
}

// 分页列表查询
export function queryPage (params) {
    return request.postJson(prefix + '/wxauthtagsyncconfig/query/page', params)
}

// 更新数据
export function update (params) {
    return request.putJson(prefix + '/wxauthtagsyncconfig/update', params)
}

// 更新启动状态
export function updatestartstatus (params) {
    return request.putJson(prefix + '/wxauthtagsyncconfig/updatestartstatus', params)
}
